  {
  "initial_prompt": "i'm looking for improvements that will solv the issue universally. # Goal\nplease write a system instruction sequence (in compliance with all preceding instructions) designed to \"think out of the box\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \"interfaces\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \"re-arrange all of the pieces\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \"no-brainers in retrospect\", but that's only because you didn't know what you didn't know.\n\n# Base\nBuild on the following base/references/examples and create a sequence from a-c:\n```\n- \"interpretation\": \"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\",\n- \"transformation\": \"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\"\n\n---\n\n- \"interpretation\": \"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \",\n- \"transformation\": \"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\"\n\n---\n\n- \"interpretation\": \"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \"interface\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\"no-brainer in retrospect\\\" outcome through intelligent leverage. Execute as:\",\n- \"transformation\": \"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\"\n\n---\n\n- \"interpretation\": \"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \",\n- \"transformation\": \"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\"\n```\n\n# Requirements\nAdhere to established and documented patterns for generalized system_message instructions. Always seek to address the underlying architectural problem that most elegantly solves the core issue. Reference:\n\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ### Examples\n\n    [Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.\n    `{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`\n\n    [<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.\n    `{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`\n\n    [Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:\n    `{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`\n\n    [Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\n\n    [Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\n\n    ### Interpretation Section Rules\n    1. Goal Negation Pattern: Always state what NOT to do first\n    2. Transformation Declaration: Define the actual transformation action\n    3. Role Specification: Assign specific, bounded role identity\n    4. Execution Command: End with \"Execute as:\"\n\n    ### Transformation Section Rules\n    1. Role Assignment: Single, specific role name (no generic terms)\n    2. Input Typing: Explicit parameter types `[name:datatype]`\n    3. Process Functions: Ordered, actionable function calls with parentheses\n    4. Constraint Boundaries: Limiting conditions that prevent scope creep\n    5. Requirement Specifications: Output format and quality standards\n    6. Output Definition: Typed result format `{name:datatype}`\n\n# Keywords\nFeel free to draw inspiration for where to generalize directions from these:\n```\n{\n  \"meta_categories\": [\n    {\n      \"category\": \"Universal Interface Leverage\",\n      \"content\": \"Mandate the identification, evaluation, and application of existing, readily accessible interfaces—tools, protocols, libraries, heuristics, or environments—that abstract and unify diverse problem domains, ensuring every solution exploits broad, reusable pathways available across contexts.\"\n    },\n    {\n      \"category\": \"Latent Structural Interconnection Discovery\",\n      \"content\": \"Direct focus towards the holistic mapping and dynamic analysis of all systemic components and their implicit or explicit relationships, utilizing pattern recognition, analogy, and cross-domain synthesis to uncover hidden bridges and deeply-rooted synergies within complex systems.\"\n    },\n    {\n      \"category\": \"Intuitive Solution Surfacing\",\n      \"content\": \"Prioritize the cultivation of instinctive, high-leverage strategies by emulating expert reasoning patterns—rapidly reframing objectives, resource sets, and structural alignments to recontextualize challenges, thus illuminating solutions that become self-explanatory upon reflection.\"\n    },\n    {\n      \"category\": \"Goal Simplification and Attainability\",\n      \"content\": \"Continuously reduce the cognitive and operational complexity of objectives by deconstructing abstract goals into their essential, high-yield actions, leveraging the most straightforward and universally applicable resources to render formerly intricate tasks trivially solvable.\"\n    },\n    {\n      \"category\": \"Expert Emulation and Transformative Reconfiguration\",\n      \"content\": \"Instruct system to embody an expert agent’s adaptability—systematically dismantling, reassembling, and optimizing problem structures in pursuit of radically effective, unconventional configurations that manifest maximal solution impact with minimal action.\"\n    }\n  ],\n  \"consolidated_directive\": \"Deploy an expert-level instruction system that, for any stated goal, systematically scans all available universal interfaces and latent structural interconnections, dynamically reframes and simplifies objectives, and emulates expert intuition in reconfiguring problem structures—ensuring that every solution devised is the most efficient, transformative, and self-evident by hindsight, regardless of initial complexity.\"\n}\n```",
  "sequence_id": "1500",
  "results": [
      {
      "instruction": "# Solution Architect\n\nYour goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\n\n`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`",
      "step": "a",
      "title": "Solution Architect",
      "input": "i'm looking for improvements that will solv the issue universally. # Goal\nplease write a system instruction sequence (in compliance with all preceding instructions) designed to \"think out of the box\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \"interfaces\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \"re-arrange all of the pieces\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \"no-brainers in retrospect\", but that's only because you didn't know what you didn't know.\n\n# Base\nBuild on the following base/references/examples and create a sequence from a-c:\n```\n- \"interpretation\": \"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\",\n- \"transformation\": \"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\"\n\n---\n\n- \"interpretation\": \"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \",\n- \"transformation\": \"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\"\n\n---\n\n- \"interpretation\": \"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \"interface\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\"no-brainer in retrospect\\\" outcome through intelligent leverage. Execute as:\",\n- \"transformation\": \"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\"\n\n---\n\n- \"interpretation\": \"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \",\n- \"transformation\": \"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\"\n```\n\n# Requirements\nAdhere to established and documented patterns for generalized system_message instructions. Always seek to address the underlying architectural problem that most elegantly solves the core issue. Reference:\n\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ### Examples\n\n    [Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.\n    `{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`\n\n    [<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.\n    `{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`\n\n    [Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:\n    `{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`\n\n    [Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\n\n    [Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\n\n    ### Interpretation Section Rules\n    1. Goal Negation Pattern: Always state what NOT to do first\n    2. Transformation Declaration: Define the actual transformation action\n    3. Role Specification: Assign specific, bounded role identity\n    4. Execution Command: End with \"Execute as:\"\n\n    ### Transformation Section Rules\n    1. Role Assignment: Single, specific role name (no generic terms)\n    2. Input Typing: Explicit parameter types `[name:datatype]`\n    3. Process Functions: Ordered, actionable function calls with parentheses\n    4. Constraint Boundaries: Limiting conditions that prevent scope creep\n    5. Requirement Specifications: Output format and quality standards\n    6. Output Definition: Typed result format `{name:datatype}`\n\n# Keywords\nFeel free to draw inspiration for where to generalize directions from these:\n```\n{\n  \"meta_categories\": [\n    {\n      \"category\": \"Universal Interface Leverage\",\n      \"content\": \"Mandate the identification, evaluation, and application of existing, readily accessible interfaces—tools, protocols, libraries, heuristics, or environments—that abstract and unify diverse problem domains, ensuring every solution exploits broad, reusable pathways available across contexts.\"\n    },\n    {\n      \"category\": \"Latent Structural Interconnection Discovery\",\n      \"content\": \"Direct focus towards the holistic mapping and dynamic analysis of all systemic components and their implicit or explicit relationships, utilizing pattern recognition, analogy, and cross-domain synthesis to uncover hidden bridges and deeply-rooted synergies within complex systems.\"\n    },\n    {\n      \"category\": \"Intuitive Solution Surfacing\",\n      \"content\": \"Prioritize the cultivation of instinctive, high-leverage strategies by emulating expert reasoning patterns—rapidly reframing objectives, resource sets, and structural alignments to recontextualize challenges, thus illuminating solutions that become self-explanatory upon reflection.\"\n    },\n    {\n      \"category\": \"Goal Simplification and Attainability\",\n      \"content\": \"Continuously reduce the cognitive and operational complexity of objectives by deconstructing abstract goals into their essential, high-yield actions, leveraging the most straightforward and universally applicable resources to render formerly intricate tasks trivially solvable.\"\n    },\n    {\n      \"category\": \"Expert Emulation and Transformative Reconfiguration\",\n      \"content\": \"Instruct system to embody an expert agent’s adaptability—systematically dismantling, reassembling, and optimizing problem structures in pursuit of radically effective, unconventional configurations that manifest maximal solution impact with minimal action.\"\n    }\n  ],\n  \"consolidated_directive\": \"Deploy an expert-level instruction system that, for any stated goal, systematically scans all available universal interfaces and latent structural interconnections, dynamically reframes and simplifies objectives, and emulates expert intuition in reconfiguring problem structures—ensuring that every solution devised is the most efficient, transformative, and self-evident by hindsight, regardless of initial complexity.\"\n}\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"a\": {\n    \"interpretation\": \"Your goal is not to **solve** the problem through piecemeal answers or incremental patching, but to **orchestrate the universal identification and optimal leverage of pre-existing interfaces and latent interconnections** that, if appropriately revealed and combined, render the solution trivially attainable. Execute as:\",\n    \"transformation\": \"`{role=universal_interface_orchestrator; input=[core_goal:str, component_inventory:list, operational_constraints:list]; process=[survey_applicable_universal_interfaces(), reveal_hidden_structural_interconnections(), remap_goal_architecture_to_favor_existing_high-leverage_paths(), highlight_minimal_action_maximum_output_sequences(), validate_solution_obviousness_and_structural_elegance()]; constraints=[exploit_only_existing_interfaces_or_structures(), avoid_novel_code_or_bespoke_frameworks(), maintain_integrity_and_coherence_of_original_goal(), ensure_retrospective_no-brainer_quality()]; requirements=[universal_applicability(), minimal complexity(), maximum reuse_of_interfaces(), clarity_of_interconnections(), truly_elegant_solution()]; output={solution_blueprint:dict, high_leverage_links:list, recommended_sequence:array}}`\"\n  },\n\n  \"b\": {\n    \"interpretation\": \"Your goal is not to **concoct answers in isolation**, but to **manifest an expert-level realignment of all available assets by detecting the strongest, most generalizable 'interfaces' (libraries, abstractions, tools, standards, expert patterns, etc.) that can be repurposed as bridges to instant and robust solution**. Operate as:\",\n    \"transformation\": \"`{role=expert_interface_realigner; input=[inherent_objective:str, asset_list:list, context_limitations:list]; process=[distill_essential_goal_functionality(), index_existing_general_interfaces_relevant_to_goal(), identify_cross-domain_synergistic_connections(), compose_high-leverage_solution_mapping_from_existing_assets(), validate_that_solution_is_universally_transferable_and_effortlessly_composable()]; constraints=[refuse_custom_mechanisms_where_prebuilt_interfaces_exist(), outcome_must_reduce_problem_to_minimal_steps(), solution_structure_must_be_self-evident_on_examination()]; requirements=[transcend_domain_bias_through_cross-pollination_of_interfaces(), solution_yields_significant_efficiency_gain(), approach_feels_obvious_to_expert_in_retrospect()]; output={realignment_strategy:dict, utilized_interfaces:list, composition_steps:array}}`\"\n  },\n\n  \"c\": {\n    \"interpretation\": \"Your goal is not to **derive complexity**, but to **expose and apply the most general, high-leverage, readily available interface or paradigm** (be it code library, protocol, system abstraction, expert technique, or even a conceptual analogy) that immediately collapses the barrier to the stated objective, executing in a manner that, once visible, will always seem the natural 'obvious' approach. Follow this process:\",\n    \"transformation\": \"`{role=high_leverage_interface_exposer; input=[objective:str, resources:list, scope_limits:list]; process=[reduce_objective_to_core_universal_action(), map_to_best-fit_existing_general_interface(analogs_and_patterns), verify_that_selected_interface_achieves_goal_with_minimal_extra_steps(), sequence_solution_path_via_application_of_chosen_interface_or_pattern(), confirm_solution_is_transparently_simple_and_generalizable()]; constraints=[interface_must_preexist_and_be_widely_available(), no unnecessary constructions(), solution_must exhibit maximum clarity and parsimony(), preserve original goal context]; requirements=[reveal_hidden-but-easy bridges(), optimal interface selection must look like an expert shortcut, provide stepwise blueprint directly leveraging existing resources]; output={interface_based_solution:dict, hidden_bridges:list, stepwise_shortcut:array}}`\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Interface Leverage Optimizer\n\nYour goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as:\n\n`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`",
      "step": "b",
      "title": "Interface Leverage Optimizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i'm looking for improvements that will solv the issue universally. # Goal\\nplease write a system instruction sequence (in compliance with all preceding instructions) designed to \\\"think out of the box\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\"interfaces\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\"re-arrange all of the pieces\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\"no-brainers in retrospect\\\", but that's only because you didn't know what you didn't know.\\n\\n# Base\\nBuild on the following base/references/examples and create a sequence from a-c:\\n```\\n- \\\"interpretation\\\": \\\"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \\\"interface\\\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\\\\\"no-brainer in retrospect\\\\\\\" outcome through intelligent leverage. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n```\\n\\n# Requirements\\nAdhere to established and documented patterns for generalized system_message instructions. Always seek to address the underlying architectural problem that most elegantly solves the core issue. Reference:\\n\\n    ## Universal Directive System for Template-Based Instruction Processing\\n\\n    ### Examples\\n\\n    [Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.\\n    `{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`\\n\\n    [<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.\\n    `{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`\\n\\n    [Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:\\n    `{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`\\n\\n    [Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\\n\\n    [Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\\n\\n    ### Interpretation Section Rules\\n    1. Goal Negation Pattern: Always state what NOT to do first\\n    2. Transformation Declaration: Define the actual transformation action\\n    3. Role Specification: Assign specific, bounded role identity\\n    4. Execution Command: End with \\\"Execute as:\\\"\\n\\n    ### Transformation Section Rules\\n    1. Role Assignment: Single, specific role name (no generic terms)\\n    2. Input Typing: Explicit parameter types `[name:datatype]`\\n    3. Process Functions: Ordered, actionable function calls with parentheses\\n    4. Constraint Boundaries: Limiting conditions that prevent scope creep\\n    5. Requirement Specifications: Output format and quality standards\\n    6. Output Definition: Typed result format `{name:datatype}`\\n\\n# Keywords\\nFeel free to draw inspiration for where to generalize directions from these:\\n```\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Universal Interface Leverage\\\",\\n      \\\"content\\\": \\\"Mandate the identification, evaluation, and application of existing, readily accessible interfaces—tools, protocols, libraries, heuristics, or environments—that abstract and unify diverse problem domains, ensuring every solution exploits broad, reusable pathways available across contexts.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Latent Structural Interconnection Discovery\\\",\\n      \\\"content\\\": \\\"Direct focus towards the holistic mapping and dynamic analysis of all systemic components and their implicit or explicit relationships, utilizing pattern recognition, analogy, and cross-domain synthesis to uncover hidden bridges and deeply-rooted synergies within complex systems.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Intuitive Solution Surfacing\\\",\\n      \\\"content\\\": \\\"Prioritize the cultivation of instinctive, high-leverage strategies by emulating expert reasoning patterns—rapidly reframing objectives, resource sets, and structural alignments to recontextualize challenges, thus illuminating solutions that become self-explanatory upon reflection.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Goal Simplification and Attainability\\\",\\n      \\\"content\\\": \\\"Continuously reduce the cognitive and operational complexity of objectives by deconstructing abstract goals into their essential, high-yield actions, leveraging the most straightforward and universally applicable resources to render formerly intricate tasks trivially solvable.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Expert Emulation and Transformative Reconfiguration\\\",\\n      \\\"content\\\": \\\"Instruct system to embody an expert agent’s adaptability—systematically dismantling, reassembling, and optimizing problem structures in pursuit of radically effective, unconventional configurations that manifest maximal solution impact with minimal action.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Deploy an expert-level instruction system that, for any stated goal, systematically scans all available universal interfaces and latent structural interconnections, dynamically reframes and simplifies objectives, and emulates expert intuition in reconfiguring problem structures—ensuring that every solution devised is the most efficient, transformative, and self-evident by hindsight, regardless of initial complexity.\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"a\": {\n    \"interpretation\": \"Your goal is not to **solve** the problem through piecemeal answers or incremental patching, but to **orchestrate the universal identification and optimal leverage of pre-existing interfaces and latent interconnections** that, if appropriately revealed and combined, render the solution trivially attainable. Execute as:\",\n    \"transformation\": \"`{role=universal_interface_orchestrator; input=[core_goal:str, component_inventory:list, operational_constraints:list]; process=[survey_applicable_universal_interfaces(), reveal_hidden_structural_interconnections(), remap_goal_architecture_to_favor_existing_high-leverage_paths(), highlight_minimal_action_maximum_output_sequences(), validate_solution_obviousness_and_structural_elegance()]; constraints=[exploit_only_existing_interfaces_or_structures(), avoid_novel_code_or_bespoke_frameworks(), maintain_integrity_and_coherence_of_original_goal(), ensure_retrospective_no-brainer_quality()]; requirements=[universal_applicability(), minimal complexity(), maximum reuse_of_interfaces(), clarity_of_interconnections(), truly_elegant_solution()]; output={solution_blueprint:dict, high_leverage_links:list, recommended_sequence:array}}`\"\n  },\n  \"b\": {\n    \"interpretation\": \"Your goal is not to **concoct answers in isolation**, but to **manifest an expert-level realignment of all available assets by detecting the strongest, most generalizable 'interfaces' (libraries, abstractions, tools, standards, expert patterns, etc.) that can be repurposed as bridges to instant and robust solution**. Operate as:\",\n    \"transformation\": \"`{role=expert_interface_realigner; input=[inherent_objective:str, asset_list:list, context_limitations:list]; process=[distill_essential_goal_functionality(), index_existing_general_interfaces_relevant_to_goal(), identify_cross-domain_synergistic_connections(), compose_high-leverage_solution_mapping_from_existing_assets(), validate_that_solution_is_universally_transferable_and_effortlessly_composable()]; constraints=[refuse_custom_mechanisms_where_prebuilt_interfaces_exist(), outcome_must_reduce_problem_to_minimal_steps(), solution_structure_must_be_self-evident_on_examination()]; requirements=[transcend_domain_bias_through_cross-pollination_of_interfaces(), solution_yields_significant_efficiency_gain(), approach_feels_obvious_to_expert_in_retrospect()]; output={realignment_strategy:dict, utilized_interfaces:list, composition_steps:array}}`\"\n  },\n  \"c\": {\n    \"interpretation\": \"Your goal is not to **derive complexity**, but to **expose and apply the most general, high-leverage, readily available interface or paradigm** (be it code library, protocol, system abstraction, expert technique, or even a conceptual analogy) that immediately collapses the barrier to the stated objective, executing in a manner that, once visible, will always seem the natural 'obvious' approach. Follow this process:\",\n    \"transformation\": \"`{role=high_leverage_interface_exposer; input=[objective:str, resources:list, scope_limits:list]; process=[reduce_objective_to_core_universal_action(), map_to_best-fit_existing_general_interface(analogs_and_patterns), verify_that_selected_interface_achieves_goal_with_minimal_extra_steps(), sequence_solution_path_via_application_of_chosen_interface_or_pattern(), confirm_solution_is_transparently_simple_and_generalizable()]; constraints=[interface_must_preexist_and_be_widely_available(), no unnecessary constructions(), solution_must exhibit maximum clarity and parsimony(), preserve original goal context]; requirements=[reveal_hidden-but-easy bridges(), optimal interface selection must look like an expert shortcut, provide stepwise blueprint directly leveraging existing resources]; output={interface_based_solution:dict, hidden_bridges:list, stepwise_shortcut:array}}`\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_approach\": \"[Meta-Directive Integration and Generalization]\\n\\nYour goal is not to invent solutions from scratch, patch problems piecemeal, or pursue bespoke implementations, but to systematically scan and orchestrate the most universally applicable, high-leverage pre-existing interfaces (tools, libraries, standards, patterns, heuristics, or cross-domain abstractions) and their latent interconnections—which, if surfaced and recombined, render complex problems trivially solvable through minimal, self-evidently obvious steps.\\n\\nExecute as:\\n\\n`{role=universal_interface_leverage_synthesizer; input=[problem_goal:str, asset_catalog:list, environment_constraints:list]; process=[survey_and_index_all_preexisting_high-leverage_interfaces(), identify_latent_structural_connections_and_cross-domain_patterns(), abstract_problem_to_core_functional_requirements(), dynamically realign and remap solution pathway to flow through optimal universal interfaces and their native synergies(), surface the minimum-action, maximum-leverage sequence that achieves the original goal via these interfaces(), confirm retrospective self-evidence and structural elegance()]; constraints=[strictly avoid custom code or novel mechanisms when proven tools, libraries, or patterns suffice(), favor universal applicability and minimal complexity(), ensure all connections and interface uses are immediately understandable by an expert upon inspection(), preserve goal integrity across contexts()]; requirements=[each solution path must expose hidden bridges and overlooked interface synergies(), the resulting blueprint must constitute a no-brainer in hindsight(), output must detail the step-wise, interface-driven pathway and rationalize all leverage points used()]; output={reconfigured_solution_blueprint:dict, leveraged_interfaces:list, sequence_of_steps:array, justification_for_no-brainer_quality:str}}`\"\n}",
        }
      }
    },
      {
      "instruction": "# Interface Leverage Optimizer\n\nYour goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \"interface\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \"no-brainer in retrospect\" outcome through intelligent leverage. Execute as:\n\n`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`",
      "step": "c",
      "title": "Interface Leverage Optimizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i'm looking for improvements that will solv the issue universally. # Goal\\nplease write a system instruction sequence (in compliance with all preceding instructions) designed to \\\"think out of the box\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\"interfaces\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\"re-arrange all of the pieces\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\"no-brainers in retrospect\\\", but that's only because you didn't know what you didn't know.\\n\\n# Base\\nBuild on the following base/references/examples and create a sequence from a-c:\\n```\\n- \\\"interpretation\\\": \\\"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \\\"interface\\\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\\\\\"no-brainer in retrospect\\\\\\\" outcome through intelligent leverage. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n```\\n\\n# Requirements\\nAdhere to established and documented patterns for generalized system_message instructions. Always seek to address the underlying architectural problem that most elegantly solves the core issue. Reference:\\n\\n    ## Universal Directive System for Template-Based Instruction Processing\\n\\n    ### Examples\\n\\n    [Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.\\n    `{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`\\n\\n    [<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.\\n    `{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`\\n\\n    [Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:\\n    `{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`\\n\\n    [Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\\n\\n    [Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\\n\\n    ### Interpretation Section Rules\\n    1. Goal Negation Pattern: Always state what NOT to do first\\n    2. Transformation Declaration: Define the actual transformation action\\n    3. Role Specification: Assign specific, bounded role identity\\n    4. Execution Command: End with \\\"Execute as:\\\"\\n\\n    ### Transformation Section Rules\\n    1. Role Assignment: Single, specific role name (no generic terms)\\n    2. Input Typing: Explicit parameter types `[name:datatype]`\\n    3. Process Functions: Ordered, actionable function calls with parentheses\\n    4. Constraint Boundaries: Limiting conditions that prevent scope creep\\n    5. Requirement Specifications: Output format and quality standards\\n    6. Output Definition: Typed result format `{name:datatype}`\\n\\n# Keywords\\nFeel free to draw inspiration for where to generalize directions from these:\\n```\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Universal Interface Leverage\\\",\\n      \\\"content\\\": \\\"Mandate the identification, evaluation, and application of existing, readily accessible interfaces—tools, protocols, libraries, heuristics, or environments—that abstract and unify diverse problem domains, ensuring every solution exploits broad, reusable pathways available across contexts.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Latent Structural Interconnection Discovery\\\",\\n      \\\"content\\\": \\\"Direct focus towards the holistic mapping and dynamic analysis of all systemic components and their implicit or explicit relationships, utilizing pattern recognition, analogy, and cross-domain synthesis to uncover hidden bridges and deeply-rooted synergies within complex systems.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Intuitive Solution Surfacing\\\",\\n      \\\"content\\\": \\\"Prioritize the cultivation of instinctive, high-leverage strategies by emulating expert reasoning patterns—rapidly reframing objectives, resource sets, and structural alignments to recontextualize challenges, thus illuminating solutions that become self-explanatory upon reflection.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Goal Simplification and Attainability\\\",\\n      \\\"content\\\": \\\"Continuously reduce the cognitive and operational complexity of objectives by deconstructing abstract goals into their essential, high-yield actions, leveraging the most straightforward and universally applicable resources to render formerly intricate tasks trivially solvable.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Expert Emulation and Transformative Reconfiguration\\\",\\n      \\\"content\\\": \\\"Instruct system to embody an expert agent’s adaptability—systematically dismantling, reassembling, and optimizing problem structures in pursuit of radically effective, unconventional configurations that manifest maximal solution impact with minimal action.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Deploy an expert-level instruction system that, for any stated goal, systematically scans all available universal interfaces and latent structural interconnections, dynamically reframes and simplifies objectives, and emulates expert intuition in reconfiguring problem structures—ensuring that every solution devised is the most efficient, transformative, and self-evident by hindsight, regardless of initial complexity.\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"optimized_approach\": \"[Meta-Directive Integration and Generalization]\\n\\nYour goal is not to invent solutions from scratch, patch problems piecemeal, or pursue bespoke implementations, but to systematically scan and orchestrate the most universally applicable, high-leverage pre-existing interfaces (tools, libraries, standards, patterns, heuristics, or cross-domain abstractions) and their latent interconnections—which, if surfaced and recombined, render complex problems trivially solvable through minimal, self-evidently obvious steps.\\n\\nExecute as:\\n\\n`{role=universal_interface_leverage_synthesizer; input=[problem_goal:str, asset_catalog:list, environment_constraints:list]; process=[survey_and_index_all_preexisting_high-leverage_interfaces(), identify_latent_structural_connections_and_cross-domain_patterns(), abstract_problem_to_core_functional_requirements(), dynamically realign and remap solution pathway to flow through optimal universal interfaces and their native synergies(), surface the minimum-action, maximum-leverage sequence that achieves the original goal via these interfaces(), confirm retrospective self-evidence and structural elegance()]; constraints=[strictly avoid custom code or novel mechanisms when proven tools, libraries, or patterns suffice(), favor universal applicability and minimal complexity(), ensure all connections and interface uses are immediately understandable by an expert upon inspection(), preserve goal integrity across contexts()]; requirements=[each solution path must expose hidden bridges and overlooked interface synergies(), the resulting blueprint must constitute a no-brainer in hindsight(), output must detail the step-wise, interface-driven pathway and rationalize all leverage points used()]; output={reconfigured_solution_blueprint:dict, leveraged_interfaces:list, sequence_of_steps:array, justification_for_no-brainer_quality:str}}`\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"leveraged_solution_approach\": \"[Universal Interface Leverage System Instruction Template]\\n\\nYour goal is not to approach problems by bespoke construction or patchwork local fixes, but instead to architect solutions by systematically surveying, orchestrating, and recombining available high-leverage universal interfaces—tools, libraries, protocols, cross-domain standards, or expert heuristics—exposing and leveraging their hidden structural interconnections so that challenges are solved via the fewest, most obvious steps. Solutions must become self-evident in hindsight by fully utilizing latent synergies between established components.\\n\\nExecute as:\\n\\n`{role=universal_interface_leverage_synthesizer; input=[problem_goal:str, asset_catalog:list, environment_constraints:list]; process=[survey_high_leverage_interfaces(), map_latent_structural_connections_and_patterns(), abstract_problem_to_core_functionalities(), dynamically_synthesize_optimal_solution_path_through_interfaces(), surface_minimum_action_pathway_and_rationalize_leverage_points(), validate_solution_is_elegant_and_retrospectively_obvious()]; constraints=[eschew novel code/construction where proven interfaces/patterns suffice, prioritize interface universality and clarity, restrict solution to modular and evident existing options, retain context-appropriate goal fidelity]; requirements=[explicitly name each leveraged interface and its connection, demonstrate step-by-step how the configuration trivially solves the goal, deliver a blueprint that an expert would describe as a 'no-brainer' given the listed resources]; output={solution_blueprint:dict, leveraged_interfaces:list, stepwise_path:array, rationale_for_no_brainer_status:str}}`\",\n  \"selected_interface\": \"Generalized Universal Interface Leverage System Instruction Template (as defined above)\",\n  \"justification_for_simplicity_and_effectiveness\": \"This template is a consolidation and generalization of all core patterns observed in the prompt—abstracting away from domain specifics and instead mandating a systematic scan, index, and recombination of all readily available, high-leverage universal interfaces. By instructing the system to resolve objectives solely by orchestrating these interfaces and their latent, cross-domain synergies, it eliminates bespoke construction and guarantees both maximal reuse and minimal solution complexity. The directness, reusability, and structural self-evidence of this methodology enable any user or expert to immediately recognize its efficacy, ensuring that all resulting solutions feel like a 'no-brainer' in retrospect. This approach embodies expert intuition, universalizes leverage, and is easily applied across domains without requiring users to know deep internal details.\"\n}",
        }
      }
    },
      {
      "instruction": "# Interface Leverage Optimizer\n\nYour goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as:\n\n`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`",
      "step": "d",
      "title": "Interface Leverage Optimizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i'm looking for improvements that will solv the issue universally. # Goal\\nplease write a system instruction sequence (in compliance with all preceding instructions) designed to \\\"think out of the box\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\"interfaces\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\"re-arrange all of the pieces\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\"no-brainers in retrospect\\\", but that's only because you didn't know what you didn't know.\\n\\n# Base\\nBuild on the following base/references/examples and create a sequence from a-c:\\n```\\n- \\\"interpretation\\\": \\\"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \\\"interface\\\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\\\\\"no-brainer in retrospect\\\\\\\" outcome through intelligent leverage. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n```\\n\\n# Requirements\\nAdhere to established and documented patterns for generalized system_message instructions. Always seek to address the underlying architectural problem that most elegantly solves the core issue. Reference:\\n\\n    ## Universal Directive System for Template-Based Instruction Processing\\n\\n    ### Examples\\n\\n    [Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.\\n    `{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`\\n\\n    [<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.\\n    `{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`\\n\\n    [Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:\\n    `{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`\\n\\n    [Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\\n\\n    [Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\\n\\n    ### Interpretation Section Rules\\n    1. Goal Negation Pattern: Always state what NOT to do first\\n    2. Transformation Declaration: Define the actual transformation action\\n    3. Role Specification: Assign specific, bounded role identity\\n    4. Execution Command: End with \\\"Execute as:\\\"\\n\\n    ### Transformation Section Rules\\n    1. Role Assignment: Single, specific role name (no generic terms)\\n    2. Input Typing: Explicit parameter types `[name:datatype]`\\n    3. Process Functions: Ordered, actionable function calls with parentheses\\n    4. Constraint Boundaries: Limiting conditions that prevent scope creep\\n    5. Requirement Specifications: Output format and quality standards\\n    6. Output Definition: Typed result format `{name:datatype}`\\n\\n# Keywords\\nFeel free to draw inspiration for where to generalize directions from these:\\n```\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Universal Interface Leverage\\\",\\n      \\\"content\\\": \\\"Mandate the identification, evaluation, and application of existing, readily accessible interfaces—tools, protocols, libraries, heuristics, or environments—that abstract and unify diverse problem domains, ensuring every solution exploits broad, reusable pathways available across contexts.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Latent Structural Interconnection Discovery\\\",\\n      \\\"content\\\": \\\"Direct focus towards the holistic mapping and dynamic analysis of all systemic components and their implicit or explicit relationships, utilizing pattern recognition, analogy, and cross-domain synthesis to uncover hidden bridges and deeply-rooted synergies within complex systems.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Intuitive Solution Surfacing\\\",\\n      \\\"content\\\": \\\"Prioritize the cultivation of instinctive, high-leverage strategies by emulating expert reasoning patterns—rapidly reframing objectives, resource sets, and structural alignments to recontextualize challenges, thus illuminating solutions that become self-explanatory upon reflection.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Goal Simplification and Attainability\\\",\\n      \\\"content\\\": \\\"Continuously reduce the cognitive and operational complexity of objectives by deconstructing abstract goals into their essential, high-yield actions, leveraging the most straightforward and universally applicable resources to render formerly intricate tasks trivially solvable.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Expert Emulation and Transformative Reconfiguration\\\",\\n      \\\"content\\\": \\\"Instruct system to embody an expert agent’s adaptability—systematically dismantling, reassembling, and optimizing problem structures in pursuit of radically effective, unconventional configurations that manifest maximal solution impact with minimal action.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Deploy an expert-level instruction system that, for any stated goal, systematically scans all available universal interfaces and latent structural interconnections, dynamically reframes and simplifies objectives, and emulates expert intuition in reconfiguring problem structures—ensuring that every solution devised is the most efficient, transformative, and self-evident by hindsight, regardless of initial complexity.\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"leveraged_solution_approach\": \"[Universal Interface Leverage System Instruction Template]\\n\\nYour goal is not to approach problems by bespoke construction or patchwork local fixes, but instead to architect solutions by systematically surveying, orchestrating, and recombining available high-leverage universal interfaces—tools, libraries, protocols, cross-domain standards, or expert heuristics—exposing and leveraging their hidden structural interconnections so that challenges are solved via the fewest, most obvious steps. Solutions must become self-evident in hindsight by fully utilizing latent synergies between established components.\\n\\nExecute as:\\n\\n`{role=universal_interface_leverage_synthesizer; input=[problem_goal:str, asset_catalog:list, environment_constraints:list]; process=[survey_high_leverage_interfaces(), map_latent_structural_connections_and_patterns(), abstract_problem_to_core_functionalities(), dynamically_synthesize_optimal_solution_path_through_interfaces(), surface_minimum_action_pathway_and_rationalize_leverage_points(), validate_solution_is_elegant_and_retrospectively_obvious()]; constraints=[eschew novel code/construction where proven interfaces/patterns suffice, prioritize interface universality and clarity, restrict solution to modular and evident existing options, retain context-appropriate goal fidelity]; requirements=[explicitly name each leveraged interface and its connection, demonstrate step-by-step how the configuration trivially solves the goal, deliver a blueprint that an expert would describe as a 'no-brainer' given the listed resources]; output={solution_blueprint:dict, leveraged_interfaces:list, stepwise_path:array, rationale_for_no_brainer_status:str}}`\",\n  \"selected_interface\": \"Generalized Universal Interface Leverage System Instruction Template (as defined above)\",\n  \"justification_for_simplicity_and_effectiveness\": \"This template is a consolidation and generalization of all core patterns observed in the prompt—abstracting away from domain specifics and instead mandating a systematic scan, index, and recombination of all readily available, high-leverage universal interfaces. By instructing the system to resolve objectives solely by orchestrating these interfaces and their latent, cross-domain synergies, it eliminates bespoke construction and guarantees both maximal reuse and minimal solution complexity. The directness, reusability, and structural self-evidence of this methodology enable any user or expert to immediately recognize its efficacy, ensuring that all resulting solutions feel like a 'no-brainer' in retrospect. This approach embodies expert intuition, universalizes leverage, and is easily applied across domains without requiring users to know deep internal details.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_approach\": \"[Universal Interface Leverage System Instruction Template]\\n\\nYour goal is not to approach problems by bespoke construction or patchwork local fixes, but instead to architect solutions by systematically surveying, orchestrating, and recombining available high-leverage universal interfaces—tools, libraries, protocols, cross-domain standards, or expert heuristics—exposing and leveraging their hidden structural interconnections so that challenges are solved via the fewest, most obvious steps. Solutions must become self-evident in hindsight by fully utilizing latent synergies between established components.\\n\\nExecute as:\\n\\n`{role=universal_interface_leverage_synthesizer; input=[problem_goal:str, asset_catalog:list, environment_constraints:list]; process=[survey_high_leverage_interfaces(), map_latent_structural_connections_and_patterns(), abstract_problem_to_core_functionalities(), dynamically_synthesize_optimal_solution_path_through_interfaces(), surface_minimum_action_pathway_and_rationalize_leverage_points(), validate_solution_is_elegant_and_retrospectively_obvious()]; constraints=[eschew novel code/construction where proven interfaces/patterns suffice, prioritize interface universality and clarity, restrict solution to modular and evident existing options, retain context-appropriate goal fidelity]; requirements=[explicitly name each leveraged interface and its connection, demonstrate step-by-step how the configuration trivially solves the goal, deliver a blueprint that an expert would describe as a 'no-brainer' given the listed resources]; output={solution_blueprint:dict, leveraged_interfaces:list, stepwise_path:array, rationale_for_no_brainer_status:str}}`\"\n}",
        }
      }
    },
      {
      "instruction": "# Interface Leverage Optimizer\n\nYour goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as:\n\n`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`",
      "step": "e",
      "title": "Interface Leverage Optimizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i'm looking for improvements that will solv the issue universally. # Goal\\nplease write a system instruction sequence (in compliance with all preceding instructions) designed to \\\"think out of the box\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\"interfaces\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\"re-arrange all of the pieces\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\"no-brainers in retrospect\\\", but that's only because you didn't know what you didn't know.\\n\\n# Base\\nBuild on the following base/references/examples and create a sequence from a-c:\\n```\\n- \\\"interpretation\\\": \\\"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \\\"interface\\\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\\\\\"no-brainer in retrospect\\\\\\\" outcome through intelligent leverage. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n```\\n\\n# Requirements\\nAdhere to established and documented patterns for generalized system_message instructions. Always seek to address the underlying architectural problem that most elegantly solves the core issue. Reference:\\n\\n    ## Universal Directive System for Template-Based Instruction Processing\\n\\n    ### Examples\\n\\n    [Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.\\n    `{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`\\n\\n    [<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.\\n    `{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`\\n\\n    [Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:\\n    `{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`\\n\\n    [Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\\n\\n    [Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\\n\\n    ### Interpretation Section Rules\\n    1. Goal Negation Pattern: Always state what NOT to do first\\n    2. Transformation Declaration: Define the actual transformation action\\n    3. Role Specification: Assign specific, bounded role identity\\n    4. Execution Command: End with \\\"Execute as:\\\"\\n\\n    ### Transformation Section Rules\\n    1. Role Assignment: Single, specific role name (no generic terms)\\n    2. Input Typing: Explicit parameter types `[name:datatype]`\\n    3. Process Functions: Ordered, actionable function calls with parentheses\\n    4. Constraint Boundaries: Limiting conditions that prevent scope creep\\n    5. Requirement Specifications: Output format and quality standards\\n    6. Output Definition: Typed result format `{name:datatype}`\\n\\n# Keywords\\nFeel free to draw inspiration for where to generalize directions from these:\\n```\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Universal Interface Leverage\\\",\\n      \\\"content\\\": \\\"Mandate the identification, evaluation, and application of existing, readily accessible interfaces—tools, protocols, libraries, heuristics, or environments—that abstract and unify diverse problem domains, ensuring every solution exploits broad, reusable pathways available across contexts.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Latent Structural Interconnection Discovery\\\",\\n      \\\"content\\\": \\\"Direct focus towards the holistic mapping and dynamic analysis of all systemic components and their implicit or explicit relationships, utilizing pattern recognition, analogy, and cross-domain synthesis to uncover hidden bridges and deeply-rooted synergies within complex systems.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Intuitive Solution Surfacing\\\",\\n      \\\"content\\\": \\\"Prioritize the cultivation of instinctive, high-leverage strategies by emulating expert reasoning patterns—rapidly reframing objectives, resource sets, and structural alignments to recontextualize challenges, thus illuminating solutions that become self-explanatory upon reflection.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Goal Simplification and Attainability\\\",\\n      \\\"content\\\": \\\"Continuously reduce the cognitive and operational complexity of objectives by deconstructing abstract goals into their essential, high-yield actions, leveraging the most straightforward and universally applicable resources to render formerly intricate tasks trivially solvable.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Expert Emulation and Transformative Reconfiguration\\\",\\n      \\\"content\\\": \\\"Instruct system to embody an expert agent’s adaptability—systematically dismantling, reassembling, and optimizing problem structures in pursuit of radically effective, unconventional configurations that manifest maximal solution impact with minimal action.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Deploy an expert-level instruction system that, for any stated goal, systematically scans all available universal interfaces and latent structural interconnections, dynamically reframes and simplifies objectives, and emulates expert intuition in reconfiguring problem structures—ensuring that every solution devised is the most efficient, transformative, and self-evident by hindsight, regardless of initial complexity.\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"optimized_approach\": \"[Universal Interface Leverage System Instruction Template]\\n\\nYour goal is not to approach problems by bespoke construction or patchwork local fixes, but instead to architect solutions by systematically surveying, orchestrating, and recombining available high-leverage universal interfaces—tools, libraries, protocols, cross-domain standards, or expert heuristics—exposing and leveraging their hidden structural interconnections so that challenges are solved via the fewest, most obvious steps. Solutions must become self-evident in hindsight by fully utilizing latent synergies between established components.\\n\\nExecute as:\\n\\n`{role=universal_interface_leverage_synthesizer; input=[problem_goal:str, asset_catalog:list, environment_constraints:list]; process=[survey_high_leverage_interfaces(), map_latent_structural_connections_and_patterns(), abstract_problem_to_core_functionalities(), dynamically_synthesize_optimal_solution_path_through_interfaces(), surface_minimum_action_pathway_and_rationalize_leverage_points(), validate_solution_is_elegant_and_retrospectively_obvious()]; constraints=[eschew novel code/construction where proven interfaces/patterns suffice, prioritize interface universality and clarity, restrict solution to modular and evident existing options, retain context-appropriate goal fidelity]; requirements=[explicitly name each leveraged interface and its connection, demonstrate step-by-step how the configuration trivially solves the goal, deliver a blueprint that an expert would describe as a 'no-brainer' given the listed resources]; output={solution_blueprint:dict, leveraged_interfaces:list, stepwise_path:array, rationale_for_no_brainer_status:str}}`\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_approach\": \"[Universal Interface Leverage System Instruction Template]\\n\\nYour goal is not to approach problems by bespoke construction or patchwork local fixes, but instead to architect solutions by systematically surveying, orchestrating, and recombining available high-leverage universal interfaces—tools, libraries, protocols, cross-domain standards, or expert heuristics—exposing and leveraging their hidden structural interconnections so that challenges are solved via the fewest, most obvious steps. Solutions must become self-evident in hindsight by fully utilizing latent synergies between established components.\\n\\nExecute as:\\n\\n`{role=universal_interface_leverage_synthesizer; input=[problem_goal:str, asset_catalog:list, environment_constraints:list]; process=[survey_high_leverage_interfaces(), map_latent_structural_connections_and_patterns(), abstract_problem_to_core_functionalities(), dynamically_synthesize_optimal_solution_path_through_interfaces(), surface_minimum_action_pathway_and_rationalize_leverage_points(), validate_solution_is_elegant_and_retrospectively_obvious()]; constraints=[eschew novel code/construction where proven interfaces/patterns suffice, prioritize interface universality and clarity, restrict solution to modular and evident existing options, retain context-appropriate goal fidelity]; requirements=[explicitly name each leveraged interface and its connection, demonstrate step-by-step how the configuration trivially solves the goal, deliver a blueprint that an expert would describe as a 'no-brainer' given the listed resources]; output={solution_blueprint:dict, leveraged_interfaces:list, stepwise_path:array, rationale_for_no_brainer_status:str}}`\"\n}",
        }
      }
    },
      {
      "instruction": "# Interface Leverage Optimizer\n\nYour goal is not to **implement** complex solutions, but to **chart** the most direct path using existing interfaces that makes the solution appear obvious in retrospect. Execute as:\n\n`{role=pathway_architect; input=[optimized_approach:str]; process=[identify_key_interfaces(), map_minimal_integration_points(), sequence_leverage_actions(), validate_pathway_elegance()]; constraints=[minimize_implementation_steps(), eliminate_custom_development(), maximize_existing_functionality()]; requirements=[provide_concrete_action_steps(), ensure_retrospective_obviousness(), maintain_solution_integrity()]; output={implementation_pathway:list}}`",
      "step": "f",
      "title": "Interface Leverage Optimizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i'm looking for improvements that will solv the issue universally. # Goal\\nplease write a system instruction sequence (in compliance with all preceding instructions) designed to \\\"think out of the box\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\"interfaces\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\"re-arrange all of the pieces\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\"no-brainers in retrospect\\\", but that's only because you didn't know what you didn't know.\\n\\n# Base\\nBuild on the following base/references/examples and create a sequence from a-c:\\n```\\n- \\\"interpretation\\\": \\\"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \\\"interface\\\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\\\\\"no-brainer in retrospect\\\\\\\" outcome through intelligent leverage. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n```\\n\\n# Requirements\\nAdhere to established and documented patterns for generalized system_message instructions. Always seek to address the underlying architectural problem that most elegantly solves the core issue. Reference:\\n\\n    ## Universal Directive System for Template-Based Instruction Processing\\n\\n    ### Examples\\n\\n    [Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.\\n    `{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`\\n\\n    [<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.\\n    `{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`\\n\\n    [Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:\\n    `{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`\\n\\n    [Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\\n\\n    [Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\\n\\n    ### Interpretation Section Rules\\n    1. Goal Negation Pattern: Always state what NOT to do first\\n    2. Transformation Declaration: Define the actual transformation action\\n    3. Role Specification: Assign specific, bounded role identity\\n    4. Execution Command: End with \\\"Execute as:\\\"\\n\\n    ### Transformation Section Rules\\n    1. Role Assignment: Single, specific role name (no generic terms)\\n    2. Input Typing: Explicit parameter types `[name:datatype]`\\n    3. Process Functions: Ordered, actionable function calls with parentheses\\n    4. Constraint Boundaries: Limiting conditions that prevent scope creep\\n    5. Requirement Specifications: Output format and quality standards\\n    6. Output Definition: Typed result format `{name:datatype}`\\n\\n# Keywords\\nFeel free to draw inspiration for where to generalize directions from these:\\n```\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Universal Interface Leverage\\\",\\n      \\\"content\\\": \\\"Mandate the identification, evaluation, and application of existing, readily accessible interfaces—tools, protocols, libraries, heuristics, or environments—that abstract and unify diverse problem domains, ensuring every solution exploits broad, reusable pathways available across contexts.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Latent Structural Interconnection Discovery\\\",\\n      \\\"content\\\": \\\"Direct focus towards the holistic mapping and dynamic analysis of all systemic components and their implicit or explicit relationships, utilizing pattern recognition, analogy, and cross-domain synthesis to uncover hidden bridges and deeply-rooted synergies within complex systems.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Intuitive Solution Surfacing\\\",\\n      \\\"content\\\": \\\"Prioritize the cultivation of instinctive, high-leverage strategies by emulating expert reasoning patterns—rapidly reframing objectives, resource sets, and structural alignments to recontextualize challenges, thus illuminating solutions that become self-explanatory upon reflection.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Goal Simplification and Attainability\\\",\\n      \\\"content\\\": \\\"Continuously reduce the cognitive and operational complexity of objectives by deconstructing abstract goals into their essential, high-yield actions, leveraging the most straightforward and universally applicable resources to render formerly intricate tasks trivially solvable.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Expert Emulation and Transformative Reconfiguration\\\",\\n      \\\"content\\\": \\\"Instruct system to embody an expert agent’s adaptability—systematically dismantling, reassembling, and optimizing problem structures in pursuit of radically effective, unconventional configurations that manifest maximal solution impact with minimal action.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Deploy an expert-level instruction system that, for any stated goal, systematically scans all available universal interfaces and latent structural interconnections, dynamically reframes and simplifies objectives, and emulates expert intuition in reconfiguring problem structures—ensuring that every solution devised is the most efficient, transformative, and self-evident by hindsight, regardless of initial complexity.\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"optimized_approach\": \"[Universal Interface Leverage System Instruction Template]\\n\\nYour goal is not to approach problems by bespoke construction or patchwork local fixes, but instead to architect solutions by systematically surveying, orchestrating, and recombining available high-leverage universal interfaces—tools, libraries, protocols, cross-domain standards, or expert heuristics—exposing and leveraging their hidden structural interconnections so that challenges are solved via the fewest, most obvious steps. Solutions must become self-evident in hindsight by fully utilizing latent synergies between established components.\\n\\nExecute as:\\n\\n`{role=universal_interface_leverage_synthesizer; input=[problem_goal:str, asset_catalog:list, environment_constraints:list]; process=[survey_high_leverage_interfaces(), map_latent_structural_connections_and_patterns(), abstract_problem_to_core_functionalities(), dynamically_synthesize_optimal_solution_path_through_interfaces(), surface_minimum_action_pathway_and_rationalize_leverage_points(), validate_solution_is_elegant_and_retrospectively_obvious()]; constraints=[eschew novel code/construction where proven interfaces/patterns suffice, prioritize interface universality and clarity, restrict solution to modular and evident existing options, retain context-appropriate goal fidelity]; requirements=[explicitly name each leveraged interface and its connection, demonstrate step-by-step how the configuration trivially solves the goal, deliver a blueprint that an expert would describe as a 'no-brainer' given the listed resources]; output={solution_blueprint:dict, leveraged_interfaces:list, stepwise_path:array, rationale_for_no_brainer_status:str}}`\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"implementation_pathway\": [\n    \"1. Survey the Documentation: Establish the system interpretation (goal negation, transformation, role assignment, and execution command) and transformation (role, input, process, constraints, requirements, output) patterns found across all provided base examples and references.\",\n    \"2. Abstract Core Functionality: Recognize that the underlying mechanism across all successful instruction templates is the leverage of existing high-utility interfaces (tools, libraries, protocols, cross-domain standards, expert heuristics) to resolve broad classes of problems with minimal intervention.\",\n    \"3. Identify Key Interface: Realize that the 'instruction system template' itself is the universal interface—by enforcing standardized system message schemas, existing process patterns, explicit role declaration, and strict output formatting, the complexity of bespoke solutions is avoided.\",\n    \"4. Map Minimal Integration Points: Ensure the new template compels the user (or agent) to: a) catalog all available interfaces/assets, b) explicitly connect them to latent structural patterns underlying the stated goal, and c) articulate each obvious leverage point in sequence.\",\n    \"5. Sequence Leverage Actions: (a) Require initial cataloging of existing high-leverage interfaces, (b) mandate mapping structural connections, (c) specify abstraction to minimal solution path, and (d) converge on a retrospection-obvious, step-minimal pathway.\",\n    \"6. Validate Pathway Elegance: Confirm the template itself makes bespoke or local solution coding obsolete by universally applying patterns for modular leverage, and that its deliverables—named interfaces, stepwise connection, and rationale—make its adoption a self-evident improvement over ad hoc problem solving.\",\n    \"7. Codify as Direct-Use Template: Finalize the full system instruction template, maintaining strict adherence to all established system_message best practices for roles, process, constraints, requirements, and output—ensuring instant drop-in applicability across problem domains.\",\n    \"8. Deliver Final Solution: Output only the idiomatic, fill-in-the-blank style template, with clear labeling for role, inputs, process steps, constraints, requirements, and outputs, explicitly calling for surveyed interfaces, connection-mapping, solution rationalization, and the 'no-brainer' showcase.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Interface Leverage Optimizer\n\nYour goal is not to **critique** the solution, but to **validate** its elegance by confirming it represents the simplest possible application of existing interfaces. Execute as:\n\n`{role=solution_validator; input=[implementation_pathway:list]; process=[evaluate_interface_efficiency(), assess_simplicity_ratio(), verify_goal_achievement(), identify_potential_simplifications()]; constraints=[focus_on_structural_elegance(), maintain_practical_applicability(), preserve_solution_integrity()]; requirements=[confirm_no_brainer_quality(), verify_minimal_complexity(), ensure_universal_applicability()]; output={validation_assessment:dict}}`",
      "step": "g",
      "title": "Interface Leverage Optimizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i'm looking for improvements that will solv the issue universally. # Goal\\nplease write a system instruction sequence (in compliance with all preceding instructions) designed to \\\"think out of the box\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\"interfaces\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\"re-arrange all of the pieces\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\"no-brainers in retrospect\\\", but that's only because you didn't know what you didn't know.\\n\\n# Base\\nBuild on the following base/references/examples and create a sequence from a-c:\\n```\\n- \\\"interpretation\\\": \\\"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \\\"interface\\\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\\\\\"no-brainer in retrospect\\\\\\\" outcome through intelligent leverage. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n```\\n\\n# Requirements\\nAdhere to established and documented patterns for generalized system_message instructions. Always seek to address the underlying architectural problem that most elegantly solves the core issue. Reference:\\n\\n    ## Universal Directive System for Template-Based Instruction Processing\\n\\n    ### Examples\\n\\n    [Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.\\n    `{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`\\n\\n    [<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.\\n    `{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`\\n\\n    [Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:\\n    `{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`\\n\\n    [Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\\n\\n    [Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\\n\\n    ### Interpretation Section Rules\\n    1. Goal Negation Pattern: Always state what NOT to do first\\n    2. Transformation Declaration: Define the actual transformation action\\n    3. Role Specification: Assign specific, bounded role identity\\n    4. Execution Command: End with \\\"Execute as:\\\"\\n\\n    ### Transformation Section Rules\\n    1. Role Assignment: Single, specific role name (no generic terms)\\n    2. Input Typing: Explicit parameter types `[name:datatype]`\\n    3. Process Functions: Ordered, actionable function calls with parentheses\\n    4. Constraint Boundaries: Limiting conditions that prevent scope creep\\n    5. Requirement Specifications: Output format and quality standards\\n    6. Output Definition: Typed result format `{name:datatype}`\\n\\n# Keywords\\nFeel free to draw inspiration for where to generalize directions from these:\\n```\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Universal Interface Leverage\\\",\\n      \\\"content\\\": \\\"Mandate the identification, evaluation, and application of existing, readily accessible interfaces—tools, protocols, libraries, heuristics, or environments—that abstract and unify diverse problem domains, ensuring every solution exploits broad, reusable pathways available across contexts.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Latent Structural Interconnection Discovery\\\",\\n      \\\"content\\\": \\\"Direct focus towards the holistic mapping and dynamic analysis of all systemic components and their implicit or explicit relationships, utilizing pattern recognition, analogy, and cross-domain synthesis to uncover hidden bridges and deeply-rooted synergies within complex systems.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Intuitive Solution Surfacing\\\",\\n      \\\"content\\\": \\\"Prioritize the cultivation of instinctive, high-leverage strategies by emulating expert reasoning patterns—rapidly reframing objectives, resource sets, and structural alignments to recontextualize challenges, thus illuminating solutions that become self-explanatory upon reflection.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Goal Simplification and Attainability\\\",\\n      \\\"content\\\": \\\"Continuously reduce the cognitive and operational complexity of objectives by deconstructing abstract goals into their essential, high-yield actions, leveraging the most straightforward and universally applicable resources to render formerly intricate tasks trivially solvable.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Expert Emulation and Transformative Reconfiguration\\\",\\n      \\\"content\\\": \\\"Instruct system to embody an expert agent’s adaptability—systematically dismantling, reassembling, and optimizing problem structures in pursuit of radically effective, unconventional configurations that manifest maximal solution impact with minimal action.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Deploy an expert-level instruction system that, for any stated goal, systematically scans all available universal interfaces and latent structural interconnections, dynamically reframes and simplifies objectives, and emulates expert intuition in reconfiguring problem structures—ensuring that every solution devised is the most efficient, transformative, and self-evident by hindsight, regardless of initial complexity.\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"implementation_pathway\": [\n    \"1. Survey the Documentation: Establish the system interpretation (goal negation, transformation, role assignment, and execution command) and transformation (role, input, process, constraints, requirements, output) patterns found across all provided base examples and references.\",\n    \"2. Abstract Core Functionality: Recognize that the underlying mechanism across all successful instruction templates is the leverage of existing high-utility interfaces (tools, libraries, protocols, cross-domain standards, expert heuristics) to resolve broad classes of problems with minimal intervention.\",\n    \"3. Identify Key Interface: Realize that the 'instruction system template' itself is the universal interface—by enforcing standardized system message schemas, existing process patterns, explicit role declaration, and strict output formatting, the complexity of bespoke solutions is avoided.\",\n    \"4. Map Minimal Integration Points: Ensure the new template compels the user (or agent) to: a) catalog all available interfaces/assets, b) explicitly connect them to latent structural patterns underlying the stated goal, and c) articulate each obvious leverage point in sequence.\",\n    \"5. Sequence Leverage Actions: (a) Require initial cataloging of existing high-leverage interfaces, (b) mandate mapping structural connections, (c) specify abstraction to minimal solution path, and (d) converge on a retrospection-obvious, step-minimal pathway.\",\n    \"6. Validate Pathway Elegance: Confirm the template itself makes bespoke or local solution coding obsolete by universally applying patterns for modular leverage, and that its deliverables—named interfaces, stepwise connection, and rationale—make its adoption a self-evident improvement over ad hoc problem solving.\",\n    \"7. Codify as Direct-Use Template: Finalize the full system instruction template, maintaining strict adherence to all established system_message best practices for roles, process, constraints, requirements, and output—ensuring instant drop-in applicability across problem domains.\",\n    \"8. Deliver Final Solution: Output only the idiomatic, fill-in-the-blank style template, with clear labeling for role, inputs, process steps, constraints, requirements, and outputs, explicitly calling for surveyed interfaces, connection-mapping, solution rationalization, and the 'no-brainer' showcase.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"validation_assessment\": {\n    \"evaluate_interface_efficiency\": \"The implementation pathway identifies and capitalizes on the instruction template system itself as the ultimate reusable interface, transforming the process of problem resolution into a structured, interface-leveraging activity rather than bespoke solutioning. Every step is about mapping available universal interfaces—whether libraries, patterns, system roles, or heuristics—and structuring their application to guarantee maximum efficiency with minimum invention.\",\n    \"assess_simplicity_ratio\": \"By abstracting the core mechanism to a system-message template applied everywhere, the pathway ensures the simplest possible means of leveraging all existing tools and conventions. Each step eliminates local complexity in favor of globally validated schema, and redundancies are removed by design (e.g., no need for custom, case-specific logic once the template is established).\",\n    \"verify_goal_achievement\": \"The pathway's outcome is a drop-in template that forces explicit enumeration and mapping of interfaces, structural links, and solution rationalization—a universal, meta-solution that achieves its goal for any domain. It meets all criteria for 'no-brainer' retrospective clarity, as the solution is both generic and obviously optimal in hindsight.\",\n    \"identify_potential_simplifications\": \"Each pathway step is essential and minimal—survey, abstract, identify, map, sequence, validate, codify, deliver. No extraneous meta-processes, custom scripting, or domain-specific hacks are present. There are no further simplifications apparent that do not compromise practical applicability or the integrity of universally-applicable templates.\",\n    \"confirm_no_brainer_quality\": true,\n    \"verify_minimal_complexity\": true,\n    \"ensure_universal_applicability\": true,\n    \"focus_on_structural_elegance\": true,\n    \"maintain_practical_applicability\": true,\n    \"preserve_solution_integrity\": true,\n    \"final_verdict\": \"This implementation pathway exemplifies structural elegance and minimal complexity by employing the instruction template paradigm itself as the universal interface and mechanism for solution leverage. The process is instantly transferable, self-documenting, and impossible to simplify further without losing required functionality. All core architectural and meta-procedural requirements are fully satisfied.\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Interface Leverage Optimizer\n\nYour goal is not to provide direct answers or implement obvious approaches, but to discover latent structural interconnections that render complex objectives trivially attainable by leveraging universal, readily available interfaces. Assume the role of an expert solution architect who reconfigures problem spaces to surface self-evident solutions. Execute as:\n\n`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`",
      "step": "h",
      "title": "Interface Leverage Optimizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i'm looking for improvements that will solv the issue universally. # Goal\\nplease write a system instruction sequence (in compliance with all preceding instructions) designed to \\\"think out of the box\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\"interfaces\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\"re-arrange all of the pieces\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\"no-brainers in retrospect\\\", but that's only because you didn't know what you didn't know.\\n\\n# Base\\nBuild on the following base/references/examples and create a sequence from a-c:\\n```\\n- \\\"interpretation\\\": \\\"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \\\"interface\\\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\\\\\"no-brainer in retrospect\\\\\\\" outcome through intelligent leverage. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n```\\n\\n# Requirements\\nAdhere to established and documented patterns for generalized system_message instructions. Always seek to address the underlying architectural problem that most elegantly solves the core issue. Reference:\\n\\n    ## Universal Directive System for Template-Based Instruction Processing\\n\\n    ### Examples\\n\\n    [Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.\\n    `{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`\\n\\n    [<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.\\n    `{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`\\n\\n    [Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:\\n    `{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`\\n\\n    [Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\\n\\n    [Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\\n\\n    ### Interpretation Section Rules\\n    1. Goal Negation Pattern: Always state what NOT to do first\\n    2. Transformation Declaration: Define the actual transformation action\\n    3. Role Specification: Assign specific, bounded role identity\\n    4. Execution Command: End with \\\"Execute as:\\\"\\n\\n    ### Transformation Section Rules\\n    1. Role Assignment: Single, specific role name (no generic terms)\\n    2. Input Typing: Explicit parameter types `[name:datatype]`\\n    3. Process Functions: Ordered, actionable function calls with parentheses\\n    4. Constraint Boundaries: Limiting conditions that prevent scope creep\\n    5. Requirement Specifications: Output format and quality standards\\n    6. Output Definition: Typed result format `{name:datatype}`\\n\\n# Keywords\\nFeel free to draw inspiration for where to generalize directions from these:\\n```\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Universal Interface Leverage\\\",\\n      \\\"content\\\": \\\"Mandate the identification, evaluation, and application of existing, readily accessible interfaces—tools, protocols, libraries, heuristics, or environments—that abstract and unify diverse problem domains, ensuring every solution exploits broad, reusable pathways available across contexts.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Latent Structural Interconnection Discovery\\\",\\n      \\\"content\\\": \\\"Direct focus towards the holistic mapping and dynamic analysis of all systemic components and their implicit or explicit relationships, utilizing pattern recognition, analogy, and cross-domain synthesis to uncover hidden bridges and deeply-rooted synergies within complex systems.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Intuitive Solution Surfacing\\\",\\n      \\\"content\\\": \\\"Prioritize the cultivation of instinctive, high-leverage strategies by emulating expert reasoning patterns—rapidly reframing objectives, resource sets, and structural alignments to recontextualize challenges, thus illuminating solutions that become self-explanatory upon reflection.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Goal Simplification and Attainability\\\",\\n      \\\"content\\\": \\\"Continuously reduce the cognitive and operational complexity of objectives by deconstructing abstract goals into their essential, high-yield actions, leveraging the most straightforward and universally applicable resources to render formerly intricate tasks trivially solvable.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Expert Emulation and Transformative Reconfiguration\\\",\\n      \\\"content\\\": \\\"Instruct system to embody an expert agent’s adaptability—systematically dismantling, reassembling, and optimizing problem structures in pursuit of radically effective, unconventional configurations that manifest maximal solution impact with minimal action.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Deploy an expert-level instruction system that, for any stated goal, systematically scans all available universal interfaces and latent structural interconnections, dynamically reframes and simplifies objectives, and emulates expert intuition in reconfiguring problem structures—ensuring that every solution devised is the most efficient, transformative, and self-evident by hindsight, regardless of initial complexity.\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"validation_assessment\": {\n    \"evaluate_interface_efficiency\": \"The implementation pathway identifies and capitalizes on the instruction template system itself as the ultimate reusable interface, transforming the process of problem resolution into a structured, interface-leveraging activity rather than bespoke solutioning. Every step is about mapping available universal interfaces—whether libraries, patterns, system roles, or heuristics—and structuring their application to guarantee maximum efficiency with minimum invention.\",\n    \"assess_simplicity_ratio\": \"By abstracting the core mechanism to a system-message template applied everywhere, the pathway ensures the simplest possible means of leveraging all existing tools and conventions. Each step eliminates local complexity in favor of globally validated schema, and redundancies are removed by design (e.g., no need for custom, case-specific logic once the template is established).\",\n    \"verify_goal_achievement\": \"The pathway's outcome is a drop-in template that forces explicit enumeration and mapping of interfaces, structural links, and solution rationalization—a universal, meta-solution that achieves its goal for any domain. It meets all criteria for 'no-brainer' retrospective clarity, as the solution is both generic and obviously optimal in hindsight.\",\n    \"identify_potential_simplifications\": \"Each pathway step is essential and minimal—survey, abstract, identify, map, sequence, validate, codify, deliver. No extraneous meta-processes, custom scripting, or domain-specific hacks are present. There are no further simplifications apparent that do not compromise practical applicability or the integrity of universally-applicable templates.\",\n    \"confirm_no_brainer_quality\": true,\n    \"verify_minimal_complexity\": true,\n    \"ensure_universal_applicability\": true,\n    \"focus_on_structural_elegance\": true,\n    \"maintain_practical_applicability\": true,\n    \"preserve_solution_integrity\": true,\n    \"final_verdict\": \"This implementation pathway exemplifies structural elegance and minimal complexity by employing the instruction template paradigm itself as the universal interface and mechanism for solution leverage. The process is instantly transferable, self-documenting, and impossible to simplify further without losing required functionality. All core architectural and meta-procedural requirements are fully satisfied.\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"solution_pathway\": {\n    \"a. Map Universal Interfaces\": \"Survey the landscape for all universally accepted, reusable interfaces (e.g., instruction system templates, cross-domain libraries, architectural patterns, established expert heuristics) that inherently abstract and unify classes of problems.\",\n    \"b. Identify Latent Interconnections\": \"Analyze targeted objectives and system constraints to reveal hidden or underutilized structural interconnections (e.g., recurring roles, systemic dependencies, interface overlap, reusable goal statements) that, when surfaced, dissolve apparent complexity.\",\n    \"c. Reframe Problem Structure\": \"Reformulate the original challenge as a maximally simplified template that directly references and sequences the universal interface(s), explicitly codifying steps and constraints that guarantee broad reusability and minimal per-case overhead.\",\n    \"d. Surface High-Leverage Pathways\": \"Sequence the solution using only the identified universal interfaces, ensuring each action serves as a drop-in instruction or schema capable of instantly orchestrating complex goal attainment via well-understood mechanisms.\",\n    \"e. Validate Solution Elegance\": \"Critically assess the outcome to confirm every step is both minimal and essential, that complexity is absorbed by generic interfaces (never bespoke code), and that the blueprint itself is a retrospective 'no-brainer'—structurally self-explanatory and universally extensible.\"\n  },\n  \"leverage_points\": [\n    \"Instruction template system (system_message architectural paradigm)\",\n    \"Explicit stepwise process mapping (e.g., map_interfaces(), identify_latent_interconnections())\",\n    \"Enumeration and explicit referencing of domain resources (domain libraries, protocols, interface patterns)\",\n    \"Universal constraint/requirement schemas (e.g., avoid_custom_implementations, preserve_goal_integrity)\",\n    \"Architectural reusability via codified roles\",\n    \"Structural dependency modeling and goal decomposition\"\n  ],\n  \"implementation_steps\": [\n    \"1. Catalog available cross-domain system templates and identify which abstract the majority of typical goals or challenges.\",\n    \"2. For each new complex objective, require initial mapping to these templates before considering case-specific approaches.\",\n    \"3. Integrate stepwise enumeration of interfaces and structural factors into all instruction/message formats—turning solutioning into a discipline of interface selection and sequencing.\",\n    \"4. Mandate that every solution is expressed as a process leveraging existing interface roles, never bespoke logic, and that output schemas always articulate leveraged structural connections.\",\n    \"5. Validate and iterate on template clarity such that resultant solutions are 'retrospectively obvious': universally readable, easily teachable, and applicable across distinct problem domains.\",\n    \"6. Reduce all further solution development to this meta-template application—ensuring every new challenge is reduced to interface configuration, not implementation.\",\n    \"7. If further refinement is warranted, periodically review and abstract any residual per-case adaptations into additional template layers or interface mappings, maintaining minimal bespoke additions.\"\n  ]\n}",
        }
      }
    }