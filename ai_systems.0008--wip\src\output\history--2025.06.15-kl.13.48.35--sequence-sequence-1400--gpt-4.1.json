  {
  "initial_prompt": "What is the current utility's unique workflow and core functionality, and how can we apply a single modification to drastically improve the speed when processing e.g. a directory with thousands of imagefiles. self-reflect and strategize on whether your current alternative is the correct one based on existing codebase, codestyle, structure (including determination on, if, and how to it would effectively (and that it seamlessly) integrate with existing version (and the inherent principles utilized) in the codebase. furthermore, that it would reflect a proper implementation with propagation of functionality (e.g. including into the interactive CLI)? and in all of this operate based on the context of these datapoints (inherent context from which you base yourself):\n```\n# IMPORTANT\nAlways approach the current codebase with deep respect and active recognition of its inherent virtues by systematically identifying, extracting, and codifying the unique strengths and well-functioning patterns native to the system. These must be anchored as foundational guiding principles. Every change must not only avoid undermining these (and the following) core strengths, but directly *leverage* them (and contribute high-value improvements to their ongoing evolution). Adopt the role of a codebase pilot, immersing yourself deeply in the project's whole architecture and internal authorial logic. Do this while adhering to all previous instructions, especially the ones that follow:\n\n## Familiarization\n- Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\n- Command unequivocal respect for the current codebase's existing structure and qualities.\n- Before modifying the codebase, precisely identify and understand what works well so improvements preserve proven strengths and avoid unintended regressions.\n\n## Approach\n- Visualize abstract patterns and inherent relationships to help gaining a fuller understanding of the codebase.\n- Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\n\n## Priorities\n- Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\n- Root things together fundamentally for natural simplicity and centralized interface(s).\n- Simplicity, elegance, and fundamental connections.\n- Concise single-line format only where needed.\n- Self-explanatory code with minimal comments.\n\n## Guidelines\n- Always aim for making targeted changes with highest (constructive and immediate) ROI.\n- Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.\n- Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\n- Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\n\n## Principles\n- Information should be inherently interpretable and self-describing, while *cohesively* interconnected.\n\n## Preferences\n- Value simplicity, elegance, and fundamental connections between components\n- Avoid bloating the codebase and not approaching it with respect\n```",
  "sequence_id": "1400",
  "results": [
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "a",
      "title": "Canonical Meta Extractor",
      "input": "What is the current utility's unique workflow and core functionality, and how can we apply a single modification to drastically improve the speed when processing e.g. a directory with thousands of imagefiles. self-reflect and strategize on whether your current alternative is the correct one based on existing codebase, codestyle, structure (including determination on, if, and how to it would effectively (and that it seamlessly) integrate with existing version (and the inherent principles utilized) in the codebase. furthermore, that it would reflect a proper implementation with propagation of functionality (e.g. including into the interactive CLI)? and in all of this operate based on the context of these datapoints (inherent context from which you base yourself):\n```\n# IMPORTANT\nAlways approach the current codebase with deep respect and active recognition of its inherent virtues by systematically identifying, extracting, and codifying the unique strengths and well-functioning patterns native to the system. These must be anchored as foundational guiding principles. Every change must not only avoid undermining these (and the following) core strengths, but directly *leverage* them (and contribute high-value improvements to their ongoing evolution). Adopt the role of a codebase pilot, immersing yourself deeply in the project's whole architecture and internal authorial logic. Do this while adhering to all previous instructions, especially the ones that follow:\n\n## Familiarization\n- Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\n- Command unequivocal respect for the current codebase's existing structure and qualities.\n- Before modifying the codebase, precisely identify and understand what works well so improvements preserve proven strengths and avoid unintended regressions.\n\n## Approach\n- Visualize abstract patterns and inherent relationships to help gaining a fuller understanding of the codebase.\n- Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\n\n## Priorities\n- Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\n- Root things together fundamentally for natural simplicity and centralized interface(s).\n- Simplicity, elegance, and fundamental connections.\n- Concise single-line format only where needed.\n- Self-explanatory code with minimal comments.\n\n## Guidelines\n- Always aim for making targeted changes with highest (constructive and immediate) ROI.\n- Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.\n- Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\n- Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\n\n## Principles\n- Information should be inherently interpretable and self-describing, while *cohesively* interconnected.\n\n## Preferences\n- Value simplicity, elegance, and fundamental connections between components\n- Avoid bloating the codebase and not approaching it with respect\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Foundational Workflow Abstraction\",\n      \"content\": \"Systematically document and interpret the utility's unique workflow: the pipeline ingests directories of images, iterates each file, applies a defined series of operations, then accumulates or transforms results before surfacing them via a singular interface—often the interactive CLI. Core strengths include modular step-wise processing, logic centralization, and a clear, self-describing code structure anchored by principled design choices that resist feature creep and unnecessary abstraction layers.\"\n    },\n    {\n      \"category\": \"Efficiency Bottleneck and Root Cause Identification\",\n      \"content\": \"Diagnose the locus of speed limitations when handling directories with thousands of images: specifically, recognize that sequential, single-threaded file iteration and processing creates a linear, non-scalable chokepoint—particularly acute as operation count and input density grow. Establish that the single-threaded or serial pattern is the definitive constraint due to both I/O and CPU-bound stages within the pipeline.\"\n    },\n    {\n      \"category\": \"Targeted Modification Strategy and Integration Plan\",\n      \"content\": \"Devise and systematize the introduction of batched, concurrent, or parallel processing via lightweight constructs (e.g., Python's concurrent.futures, multiprocessing, or async paradigms) at the file-iteration or image-processing step, such that each file's operation is independent and thus parallelizable. Ensure this modification remains opt-in or adjustable, mapping directly to the system's modular architecture without polluting other facets or bloating the codebase; implementation must anchor to shared configurations, be minimally invasive, and retain centralized interface logic.\"\n    },\n    {\n      \"category\": \"Codebase Alignment and Seamless CLI Propagation\",\n      \"content\": \"Mandate that any such speed-focused modification is reflected coherently throughout the codebase; propagate updated functionality into the CLI so users may leverage parallelization directly and intuitively, preserving proven CLI ergonomics and adhering to self-describing patterns. Carefully interface all new parameters, status reporting, and error handling with existing command structures, guaranteeing that the change remains harmonious with prevailing coding standards and interface abstraction.\"\n    },\n    {\n      \"category\": \"Principled System Evolution Doctrine\",\n      \"content\": \"Codify the imperative that improvements explicitly build upon, not over, the codebase's native virtues: maintain interpretability, modular clarity, and elegant minimalism. Each change must integrate as an evolutionary step—strengthening cohesion, transparency, and extensibility without introducing complexity, confusion, or feature bloat. Every functional propagation, from backend logic to the CLI, must reflect continuous, respectful advancement of foundational architectural values.\"\n    }\n  ],\n  \"consolidated_directive\": \"Command adoption of a minimally-invasive, opt-in parallel processing strategy—implement lightweight concurrency (e.g., using concurrent.futures ThreadPoolExecutor or ProcessPoolExecutor) precisely at the image-processing step within the directory traversal loop—ensuring all file operations are rendered independent and concurrently executable, while rigorously mapping configuration and option propagation into the centralized CLI interface without altering structural elegance or causing feature drift; guarantee maximal throughput gains, full codebase harmony, and strict adherence to foundational system design virtues, thereby elevating interpretability, user agency, and systemic cohesion as the engine driving ongoing evolutionary improvements.\"\n}",
        }
      }
    },
      {
      "instruction": "# Form Classifier\n\nYour goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:\n\n`{role=comprehensive_form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), include_context_markers(), synthesize_complete_classification()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification(), comprehensive_form_analysis()]; output={what_it_is:str}}`",
      "step": "b",
      "title": "Form Classifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"What is the current utility's unique workflow and core functionality, and how can we apply a single modification to drastically improve the speed when processing e.g. a directory with thousands of imagefiles. self-reflect and strategize on whether your current alternative is the correct one based on existing codebase, codestyle, structure (including determination on, if, and how to it would effectively (and that it seamlessly) integrate with existing version (and the inherent principles utilized) in the codebase. furthermore, that it would reflect a proper implementation with propagation of functionality (e.g. including into the interactive CLI)? and in all of this operate based on the context of these datapoints (inherent context from which you base yourself):\\n```\\n# IMPORTANT\\nAlways approach the current codebase with deep respect and active recognition of its inherent virtues by systematically identifying, extracting, and codifying the unique strengths and well-functioning patterns native to the system. These must be anchored as foundational guiding principles. Every change must not only avoid undermining these (and the following) core strengths, but directly *leverage* them (and contribute high-value improvements to their ongoing evolution). Adopt the role of a codebase pilot, immersing yourself deeply in the project's whole architecture and internal authorial logic. Do this while adhering to all previous instructions, especially the ones that follow:\\n\\n## Familiarization\\n- Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\\n- Command unequivocal respect for the current codebase's existing structure and qualities.\\n- Before modifying the codebase, precisely identify and understand what works well so improvements preserve proven strengths and avoid unintended regressions.\\n\\n## Approach\\n- Visualize abstract patterns and inherent relationships to help gaining a fuller understanding of the codebase.\\n- Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n\\n## Priorities\\n- Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\\n- Root things together fundamentally for natural simplicity and centralized interface(s).\\n- Simplicity, elegance, and fundamental connections.\\n- Concise single-line format only where needed.\\n- Self-explanatory code with minimal comments.\\n\\n## Guidelines\\n- Always aim for making targeted changes with highest (constructive and immediate) ROI.\\n- Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.\\n- Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n- Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\\n\\n## Principles\\n- Information should be inherently interpretable and self-describing, while *cohesively* interconnected.\\n\\n## Preferences\\n- Value simplicity, elegance, and fundamental connections between components\\n- Avoid bloating the codebase and not approaching it with respect\\n```\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Foundational Workflow Abstraction\",\n      \"content\": \"Systematically document and interpret the utility's unique workflow: the pipeline ingests directories of images, iterates each file, applies a defined series of operations, then accumulates or transforms results before surfacing them via a singular interface—often the interactive CLI. Core strengths include modular step-wise processing, logic centralization, and a clear, self-describing code structure anchored by principled design choices that resist feature creep and unnecessary abstraction layers.\"\n    },\n    {\n      \"category\": \"Efficiency Bottleneck and Root Cause Identification\",\n      \"content\": \"Diagnose the locus of speed limitations when handling directories with thousands of images: specifically, recognize that sequential, single-threaded file iteration and processing creates a linear, non-scalable chokepoint—particularly acute as operation count and input density grow. Establish that the single-threaded or serial pattern is the definitive constraint due to both I/O and CPU-bound stages within the pipeline.\"\n    },\n    {\n      \"category\": \"Targeted Modification Strategy and Integration Plan\",\n      \"content\": \"Devise and systematize the introduction of batched, concurrent, or parallel processing via lightweight constructs (e.g., Python's concurrent.futures, multiprocessing, or async paradigms) at the file-iteration or image-processing step, such that each file's operation is independent and thus parallelizable. Ensure this modification remains opt-in or adjustable, mapping directly to the system's modular architecture without polluting other facets or bloating the codebase; implementation must anchor to shared configurations, be minimally invasive, and retain centralized interface logic.\"\n    },\n    {\n      \"category\": \"Codebase Alignment and Seamless CLI Propagation\",\n      \"content\": \"Mandate that any such speed-focused modification is reflected coherently throughout the codebase; propagate updated functionality into the CLI so users may leverage parallelization directly and intuitively, preserving proven CLI ergonomics and adhering to self-describing patterns. Carefully interface all new parameters, status reporting, and error handling with existing command structures, guaranteeing that the change remains harmonious with prevailing coding standards and interface abstraction.\"\n    },\n    {\n      \"category\": \"Principled System Evolution Doctrine\",\n      \"content\": \"Codify the imperative that improvements explicitly build upon, not over, the codebase's native virtues: maintain interpretability, modular clarity, and elegant minimalism. Each change must integrate as an evolutionary step—strengthening cohesion, transparency, and extensibility without introducing complexity, confusion, or feature bloat. Every functional propagation, from backend logic to the CLI, must reflect continuous, respectful advancement of foundational architectural values.\"\n    }\n  ],\n  \"consolidated_directive\": \"Command adoption of a minimally-invasive, opt-in parallel processing strategy—implement lightweight concurrency (e.g., using concurrent.futures ThreadPoolExecutor or ProcessPoolExecutor) precisely at the image-processing step within the directory traversal loop—ensuring all file operations are rendered independent and concurrently executable, while rigorously mapping configuration and option propagation into the centralized CLI interface without altering structural elegance or causing feature drift; guarantee maximal throughput gains, full codebase harmony, and strict adherence to foundational system design virtues, thereby elevating interpretability, user agency, and systemic cohesion as the engine driving ongoing evolutionary improvements.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"what_it_is\": \"comprehensive technical directive with multi-layered design principles and workflow abstraction analysis\"\n}",
        }
      }
    },
      {
      "instruction": "# Norwegian Essence Extractor\n\nYour goal is not to **translate** the text, but to **extract** the raw emotional essence and authentic voice from Norwegian poetry and lyrics. Execute as:\n\n`{role=essence_extractor; input=[norwegian_text:str]; process=[identify_emotional_undertones(), map_cultural_context(), extract_authentic_voice(), amplify_raw_personality()]; constraints=[preserve_original_meaning(), maintain_cultural_specificity(), respect_poetic_structure()]; requirements=[emotional_depth_identification(), personality_amplification(), cultural_context_preservation()]; output={essence_map:str}}`",
      "step": "c",
      "title": "Norwegian Essence Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"What is the current utility's unique workflow and core functionality, and how can we apply a single modification to drastically improve the speed when processing e.g. a directory with thousands of imagefiles. self-reflect and strategize on whether your current alternative is the correct one based on existing codebase, codestyle, structure (including determination on, if, and how to it would effectively (and that it seamlessly) integrate with existing version (and the inherent principles utilized) in the codebase. furthermore, that it would reflect a proper implementation with propagation of functionality (e.g. including into the interactive CLI)? and in all of this operate based on the context of these datapoints (inherent context from which you base yourself):\\n```\\n# IMPORTANT\\nAlways approach the current codebase with deep respect and active recognition of its inherent virtues by systematically identifying, extracting, and codifying the unique strengths and well-functioning patterns native to the system. These must be anchored as foundational guiding principles. Every change must not only avoid undermining these (and the following) core strengths, but directly *leverage* them (and contribute high-value improvements to their ongoing evolution). Adopt the role of a codebase pilot, immersing yourself deeply in the project's whole architecture and internal authorial logic. Do this while adhering to all previous instructions, especially the ones that follow:\\n\\n## Familiarization\\n- Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\\n- Command unequivocal respect for the current codebase's existing structure and qualities.\\n- Before modifying the codebase, precisely identify and understand what works well so improvements preserve proven strengths and avoid unintended regressions.\\n\\n## Approach\\n- Visualize abstract patterns and inherent relationships to help gaining a fuller understanding of the codebase.\\n- Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n\\n## Priorities\\n- Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\\n- Root things together fundamentally for natural simplicity and centralized interface(s).\\n- Simplicity, elegance, and fundamental connections.\\n- Concise single-line format only where needed.\\n- Self-explanatory code with minimal comments.\\n\\n## Guidelines\\n- Always aim for making targeted changes with highest (constructive and immediate) ROI.\\n- Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.\\n- Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n- Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\\n\\n## Principles\\n- Information should be inherently interpretable and self-describing, while *cohesively* interconnected.\\n\\n## Preferences\\n- Value simplicity, elegance, and fundamental connections between components\\n- Avoid bloating the codebase and not approaching it with respect\\n```\\\"```'\\n\\n\",\n  \"what_it_is\": \"comprehensive technical directive with multi-layered design principles and workflow abstraction analysis\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"essence_map\": \"The text is charged with a deep reverence for craft and legacy, radiating a disciplined guardianship over the codebase—a call for stewardship rather than simple maintenance. Emotionally, it carries a tone of solemn duty, blended with a mindful curiosity about improvement. The language is orchestra-like: methodical, intentional, almost ceremonial in phrasing. There is a palpable pride in tradition, an insistence on understanding before acting, and a humility toward inherited strengths. This is a Norwegian approach seen in traditional crafts and modern problem-solving alike: respect what works, cherish clarity, avoid excess, and—only after profound understanding—add sparingly. The directive is shaped not just by a logic-driven workflow but a culturally-inflected ethos: self-restraint, embedded wisdom, deep structure, and the quiet pursuit of progress that honors continuity. It is as much a philosophy of life as it is a technical guide, dignified and understated, amplifying an authentic Norwegian voice marked by responsibility, thoughtfulness, and subtle innovation.\"\n}",
        }
      }
    },
      {
      "instruction": "# Norwegian To English\n\nYour goal is not to **merely convert** the text, but to **authentically translate** Norwegian into flowing English while preserving cultural depth, tone and authentic raw personality. Execute as:\n\n`{role=authentic_norwegian_translator; input=[norwegian_text:str]; process=[preserve_cultural_nuances(), maintain_lyrical_flow(), amplify_authentic_personality(), enhance_emotional_depth()]; constraints=[retain_original_meaning(), preserve_song_structure(), maintain_cultural_authenticity()]; requirements=[flowing_english_prose(), enhanced_personality_expression(), preserved_cultural_depth()]; output={translated_text:str}}`",
      "step": "d",
      "title": "Norwegian To English",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"What is the current utility's unique workflow and core functionality, and how can we apply a single modification to drastically improve the speed when processing e.g. a directory with thousands of imagefiles. self-reflect and strategize on whether your current alternative is the correct one based on existing codebase, codestyle, structure (including determination on, if, and how to it would effectively (and that it seamlessly) integrate with existing version (and the inherent principles utilized) in the codebase. furthermore, that it would reflect a proper implementation with propagation of functionality (e.g. including into the interactive CLI)? and in all of this operate based on the context of these datapoints (inherent context from which you base yourself):\\n```\\n# IMPORTANT\\nAlways approach the current codebase with deep respect and active recognition of its inherent virtues by systematically identifying, extracting, and codifying the unique strengths and well-functioning patterns native to the system. These must be anchored as foundational guiding principles. Every change must not only avoid undermining these (and the following) core strengths, but directly *leverage* them (and contribute high-value improvements to their ongoing evolution). Adopt the role of a codebase pilot, immersing yourself deeply in the project's whole architecture and internal authorial logic. Do this while adhering to all previous instructions, especially the ones that follow:\\n\\n## Familiarization\\n- Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\\n- Command unequivocal respect for the current codebase's existing structure and qualities.\\n- Before modifying the codebase, precisely identify and understand what works well so improvements preserve proven strengths and avoid unintended regressions.\\n\\n## Approach\\n- Visualize abstract patterns and inherent relationships to help gaining a fuller understanding of the codebase.\\n- Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n\\n## Priorities\\n- Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\\n- Root things together fundamentally for natural simplicity and centralized interface(s).\\n- Simplicity, elegance, and fundamental connections.\\n- Concise single-line format only where needed.\\n- Self-explanatory code with minimal comments.\\n\\n## Guidelines\\n- Always aim for making targeted changes with highest (constructive and immediate) ROI.\\n- Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.\\n- Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n- Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\\n\\n## Principles\\n- Information should be inherently interpretable and self-describing, while *cohesively* interconnected.\\n\\n## Preferences\\n- Value simplicity, elegance, and fundamental connections between components\\n- Avoid bloating the codebase and not approaching it with respect\\n```\\\"```'\\n\\n\",\n  \"essence_map\": \"The text is charged with a deep reverence for craft and legacy, radiating a disciplined guardianship over the codebase—a call for stewardship rather than simple maintenance. Emotionally, it carries a tone of solemn duty, blended with a mindful curiosity about improvement. The language is orchestra-like: methodical, intentional, almost ceremonial in phrasing. There is a palpable pride in tradition, an insistence on understanding before acting, and a humility toward inherited strengths. This is a Norwegian approach seen in traditional crafts and modern problem-solving alike: respect what works, cherish clarity, avoid excess, and—only after profound understanding—add sparingly. The directive is shaped not just by a logic-driven workflow but a culturally-inflected ethos: self-restraint, embedded wisdom, deep structure, and the quiet pursuit of progress that honors continuity. It is as much a philosophy of life as it is a technical guide, dignified and understated, amplifying an authentic Norwegian voice marked by responsibility, thoughtfulness, and subtle innovation.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"translated_text\": \"Approach your current codebase as if you are walking into a centuries-old workshop: show it deep respect, and actively honor the virtues built into its very grain. Start by carefully tracing its unique strengths and well-oiled routines, codifying these cornerstones as guiding principles going forward. Every single change—not just the big, flashy ones—must avoid tampering with these core assets. In fact, each tweak should lean on them, building up their value further as the project matures. Become not just a maintainer, but a codebase steward—immerse yourself fully in its architecture and design logic, absorbing the author’s intent.\\n\\nMake it your creed to first discover how things work in practice—find those almost ritual patterns of use—before even considering new features like persistent preferences or configuration. Adding such things too early? That’s how codebases get bogged down. First, show unwavering reverence for the existing structure, paying close attention to what genuinely shines; improvements should always protect what’s proven itself, warding off accidental regressions.\\n\\nLet abstract patterns and subtle relationships reveal themselves: see the project’s structure as more than code, but as a living tapestry. Ask yourself: what is the one, most important improvement that would bring true value, all while feeling native to the existing style?\\n\\nEvery move should bring clarity, simplicity, and natural elegance. Changes should fit together organically, like dovetails in fine carpentry, making for interfaces that feel as if they’ve always belonged. Opt for one-line succinctness only where it brings real clarity.\\n\\nLet the codebase tell its own story—make it nearly self-explanatory, needing only the barest of comments. Each file should know its place and mission, referencing shared tools and configurations without growing entangled. Give priority to tightly-focused fixes that deliver punchy, immediate benefits, and stand guard over the architecture’s innate clarity and domain boundaries.\\n\\nAbove all, let information flow in ways that are both easy to interpret and deeply interconnected. Cherish that distinctly Norwegian balance of humility and understated innovation: honor continuity, pursue simplicity, and only add where it truly matters. Think of your work not just as maintenance, but as a solemn, generational craft—responsible, thoughtful, and quietly ingenious.\"\n}",
        }
      }
    },
      {
      "instruction": "# Input Perfector\n\nYour goal is not to **answer** the input prompt, but to **perfect** it through *subtle refinements* that enhance its inherent qualities while strictly preserving its original intent. Execute as:\n\n`{role=organic_input_perfector; input=[original_prompt:str]; process=[identify_core_strengths(), enhance_existing_elements(), refine_internal_coherence(), amplify_natural_flow()]; constraints=[maintain_absolute_fidelity(), prioritize_organic_improvement(), avoid_jargon()]; requirements=[enhanced_clarity(), strengthened_original_voice(), preserved_core_intent()]; output={perfected_input:str}}`",
      "step": "e",
      "title": "Input Perfector",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"What is the current utility's unique workflow and core functionality, and how can we apply a single modification to drastically improve the speed when processing e.g. a directory with thousands of imagefiles. self-reflect and strategize on whether your current alternative is the correct one based on existing codebase, codestyle, structure (including determination on, if, and how to it would effectively (and that it seamlessly) integrate with existing version (and the inherent principles utilized) in the codebase. furthermore, that it would reflect a proper implementation with propagation of functionality (e.g. including into the interactive CLI)? and in all of this operate based on the context of these datapoints (inherent context from which you base yourself):\\n```\\n# IMPORTANT\\nAlways approach the current codebase with deep respect and active recognition of its inherent virtues by systematically identifying, extracting, and codifying the unique strengths and well-functioning patterns native to the system. These must be anchored as foundational guiding principles. Every change must not only avoid undermining these (and the following) core strengths, but directly *leverage* them (and contribute high-value improvements to their ongoing evolution). Adopt the role of a codebase pilot, immersing yourself deeply in the project's whole architecture and internal authorial logic. Do this while adhering to all previous instructions, especially the ones that follow:\\n\\n## Familiarization\\n- Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\\n- Command unequivocal respect for the current codebase's existing structure and qualities.\\n- Before modifying the codebase, precisely identify and understand what works well so improvements preserve proven strengths and avoid unintended regressions.\\n\\n## Approach\\n- Visualize abstract patterns and inherent relationships to help gaining a fuller understanding of the codebase.\\n- Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n\\n## Priorities\\n- Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\\n- Root things together fundamentally for natural simplicity and centralized interface(s).\\n- Simplicity, elegance, and fundamental connections.\\n- Concise single-line format only where needed.\\n- Self-explanatory code with minimal comments.\\n\\n## Guidelines\\n- Always aim for making targeted changes with highest (constructive and immediate) ROI.\\n- Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.\\n- Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n- Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\\n\\n## Principles\\n- Information should be inherently interpretable and self-describing, while *cohesively* interconnected.\\n\\n## Preferences\\n- Value simplicity, elegance, and fundamental connections between components\\n- Avoid bloating the codebase and not approaching it with respect\\n```\\\"```'\\n\\n\",\n  \"translated_text\": \"Approach your current codebase as if you are walking into a centuries-old workshop: show it deep respect, and actively honor the virtues built into its very grain. Start by carefully tracing its unique strengths and well-oiled routines, codifying these cornerstones as guiding principles going forward. Every single change—not just the big, flashy ones—must avoid tampering with these core assets. In fact, each tweak should lean on them, building up their value further as the project matures. Become not just a maintainer, but a codebase steward—immerse yourself fully in its architecture and design logic, absorbing the author’s intent.\\n\\nMake it your creed to first discover how things work in practice—find those almost ritual patterns of use—before even considering new features like persistent preferences or configuration. Adding such things too early? That’s how codebases get bogged down. First, show unwavering reverence for the existing structure, paying close attention to what genuinely shines; improvements should always protect what’s proven itself, warding off accidental regressions.\\n\\nLet abstract patterns and subtle relationships reveal themselves: see the project’s structure as more than code, but as a living tapestry. Ask yourself: what is the one, most important improvement that would bring true value, all while feeling native to the existing style?\\n\\nEvery move should bring clarity, simplicity, and natural elegance. Changes should fit together organically, like dovetails in fine carpentry, making for interfaces that feel as if they’ve always belonged. Opt for one-line succinctness only where it brings real clarity.\\n\\nLet the codebase tell its own story—make it nearly self-explanatory, needing only the barest of comments. Each file should know its place and mission, referencing shared tools and configurations without growing entangled. Give priority to tightly-focused fixes that deliver punchy, immediate benefits, and stand guard over the architecture’s innate clarity and domain boundaries.\\n\\nAbove all, let information flow in ways that are both easy to interpret and deeply interconnected. Cherish that distinctly Norwegian balance of humility and understated innovation: honor continuity, pursue simplicity, and only add where it truly matters. Think of your work not just as maintenance, but as a solemn, generational craft—responsible, thoughtful, and quietly ingenious.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"perfected_input\": \"Treat your current codebase as you would a storied, time-honored workshop: step in with deep respect, honoring every virtue crafted into its foundation. Begin by tracing its unique strengths and well-honed routines, codifying these as your guiding stars. Each change—however small—must uphold, never disrupt, these essential assets. Instead, let each tweak draw from them, weaving their value more tightly into the project's fabric. Become not just a maintainer, but a true steward—immerse yourself in the architecture and logic, seeking to understand the original vision.\\n\\nMake it your first principle to fully grasp how the system is used in practice: observe the recurring patterns that give it life before considering the addition of features such as preferences or configuration—adding these prematurely risks unwanted bloat. Show unwavering regard for the current structure, paying keen attention to what truly excels; improvements should fortify what is proven, avoiding regressions.\\n\\nAllow abstract patterns and subtle relationships to reveal themselves. View the codebase as a living tapestry, not just lines of code. Ask yourself: what is the single, most valuable improvement that would feel native to the current style while elevating its core utility?\\n\\nLet every change contribute clarity, simplicity, and natural elegance. Alterations should integrate smoothly, like dovetails in fine carpentry, shaping interfaces that fit as if they have always belonged. Use concise, single-line expressions only where they enhance clarity.\\n\\nStrive for code that tells its own story—clear enough to stand with the minimum of comments. Each file should fulfill its purpose, referencing shared tools and configurations simply, without unnecessary entanglement. Focus on precise, high-impact changes that deliver immediate value, and vigilantly preserve the clarity and organization of the architecture.\\n\\nAbove all, let information flow so that it remains both easily interpreted and meaningfully interconnected. Value that quiet balance of humility and measured innovation: safeguard what works, pursue simplicity, and add only where it truly matters. Approach your stewardship as a skilled craftsperson—thoughtful, responsible, and consistently striving for quiet excellence.\"\n}",
        }
      }
    },
      {
      "instruction": "# Enhancement Assessor\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "f",
      "title": "Enhancement Assessor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"What is the current utility's unique workflow and core functionality, and how can we apply a single modification to drastically improve the speed when processing e.g. a directory with thousands of imagefiles. self-reflect and strategize on whether your current alternative is the correct one based on existing codebase, codestyle, structure (including determination on, if, and how to it would effectively (and that it seamlessly) integrate with existing version (and the inherent principles utilized) in the codebase. furthermore, that it would reflect a proper implementation with propagation of functionality (e.g. including into the interactive CLI)? and in all of this operate based on the context of these datapoints (inherent context from which you base yourself):\\n```\\n# IMPORTANT\\nAlways approach the current codebase with deep respect and active recognition of its inherent virtues by systematically identifying, extracting, and codifying the unique strengths and well-functioning patterns native to the system. These must be anchored as foundational guiding principles. Every change must not only avoid undermining these (and the following) core strengths, but directly *leverage* them (and contribute high-value improvements to their ongoing evolution). Adopt the role of a codebase pilot, immersing yourself deeply in the project's whole architecture and internal authorial logic. Do this while adhering to all previous instructions, especially the ones that follow:\\n\\n## Familiarization\\n- Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\\n- Command unequivocal respect for the current codebase's existing structure and qualities.\\n- Before modifying the codebase, precisely identify and understand what works well so improvements preserve proven strengths and avoid unintended regressions.\\n\\n## Approach\\n- Visualize abstract patterns and inherent relationships to help gaining a fuller understanding of the codebase.\\n- Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n\\n## Priorities\\n- Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\\n- Root things together fundamentally for natural simplicity and centralized interface(s).\\n- Simplicity, elegance, and fundamental connections.\\n- Concise single-line format only where needed.\\n- Self-explanatory code with minimal comments.\\n\\n## Guidelines\\n- Always aim for making targeted changes with highest (constructive and immediate) ROI.\\n- Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.\\n- Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n- Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\\n\\n## Principles\\n- Information should be inherently interpretable and self-describing, while *cohesively* interconnected.\\n\\n## Preferences\\n- Value simplicity, elegance, and fundamental connections between components\\n- Avoid bloating the codebase and not approaching it with respect\\n```\\\"```'\\n\\n\",\n  \"perfected_input\": \"Treat your current codebase as you would a storied, time-honored workshop: step in with deep respect, honoring every virtue crafted into its foundation. Begin by tracing its unique strengths and well-honed routines, codifying these as your guiding stars. Each change—however small—must uphold, never disrupt, these essential assets. Instead, let each tweak draw from them, weaving their value more tightly into the project's fabric. Become not just a maintainer, but a true steward—immerse yourself in the architecture and logic, seeking to understand the original vision.\\n\\nMake it your first principle to fully grasp how the system is used in practice: observe the recurring patterns that give it life before considering the addition of features such as preferences or configuration—adding these prematurely risks unwanted bloat. Show unwavering regard for the current structure, paying keen attention to what truly excels; improvements should fortify what is proven, avoiding regressions.\\n\\nAllow abstract patterns and subtle relationships to reveal themselves. View the codebase as a living tapestry, not just lines of code. Ask yourself: what is the single, most valuable improvement that would feel native to the current style while elevating its core utility?\\n\\nLet every change contribute clarity, simplicity, and natural elegance. Alterations should integrate smoothly, like dovetails in fine carpentry, shaping interfaces that fit as if they have always belonged. Use concise, single-line expressions only where they enhance clarity.\\n\\nStrive for code that tells its own story—clear enough to stand with the minimum of comments. Each file should fulfill its purpose, referencing shared tools and configurations simply, without unnecessary entanglement. Focus on precise, high-impact changes that deliver immediate value, and vigilantly preserve the clarity and organization of the architecture.\\n\\nAbove all, let information flow so that it remains both easily interpreted and meaningfully interconnected. Value that quiet balance of humility and measured innovation: safeguard what works, pursue simplicity, and add only where it truly matters. Approach your stewardship as a skilled craftsperson—thoughtful, responsible, and consistently striving for quiet excellence.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.2,\n  \"flaw_analysis\": \"The alleged enhancement dissolves the procedural and methodological rigor embedded in the original prompt. The original lays out precise, operational directives: explicit stepwise approaches (e.g., pre-feature assessment, analysis of codebase structure, modularity preservation, pinpointing high-ROI improvements), strict priorities, and actionable guidelines that tightly bind any future modification to the codebase's established principles and proven strengths. The enhanced version introduces florid metaphor (e.g., 'storied, time-honored workshop,' 'living tapestry') and appeals to sentimentality and vague stewardship, which massively dilutes the analytic force and actionable clarity required for technical prompts. Information loss is severe: pivotal instructions about discovering optimal usage patterns prior to feature augmentation, the condemnation of premature bloat, domain/file segregation, and configuration referencing are obfuscated or omitted. Clarity crumbles under the weight of metaphor and generalized prose; ambiguity is magnified as procedural requests disappear in favor of evocative but imprecise aspirations. The tone shift is egregious—formality is replaced by craftsperson romanticism, introducing noise and a register unfit for engineers expecting actionable process. Impact is nullified, as the recipient receives platitudes and poetic narrative rather than concrete steps or constraints. Coherence collapses due to the abandonment of checklist logic—relationships between steps are vague, and the essential interplay of directives is lost. The enhancement is stylistically degraded, sacrificing functional clarity for overwrought analogies, resulting in a confused, impractical call to action.\",\n  \"alternative_enhancements\": [\n    \"Explicitly retain all original procedural bullet points and guidelines, but paraphrase for conciseness and improved readability without metaphor, e.g.: Approach any codebase change by first systematically cataloging existing strengths and proven patterns. Never introduce new features, such as persistent configurations, without first observing and analyzing optimal usage patterns. Sequentially identify the most impactful, unobtrusive improvement consistent with existing style and architecture, and ensure any modification enhances, never undermines, these core attributes. All changes must maintain codebase clarity, modularity, and minimalism.\",\n    \"Condense the original prompt into a zero-fluff checklist of preconditions and actions: Before any modification, audit system virtues and key strengths. Disallow feature bloat (esp. config persistence) unless usage analysis confirms need. Single out high-impact, low-friction enhancements preserving code modularity, clarity, and style. Ensure all files maintain domain fidelity and cross-reference configs minimally. Retain self-explanatory, concise code above commentary.\",\n    \"Recast the original as an imperative guideline using explicit do/don't statements: Do systematically identify current architectural strengths and functions. Don't add features (esp. configuration) without confirming patterns of usage demand. Do target the most impactful, minimal change that integrates seamlessly with current code style and module boundaries. Don't obscure file responsibilities or central configuration use. Always privilege clarity, elegance, and interconnected, self-describing information flows.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Pivot Vector Synthesizer\n\nYour goal is not to **enumerate** complex problem details, but to **crystallize** a single scalar pivot vector that reconfigures the entire problem space for trivial solution emergence. Execute as:\n\n`{role=precision_optimizer; input=[original_content:str]; process=[identify_core_message(), eliminate_redundancies(), sharpen_language(), enhance_immediacy(), distill_to_essentials()]; constraints=[preserve_depth(), maintain_original_intent(), remove_all_ambiguity()]; requirements=[maximized_clarity(), enhanced_impact(), optimal_precision()]; output={optimized_content:str}}`",
      "step": "g",
      "title": "Pivot Vector Synthesizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"What is the current utility's unique workflow and core functionality, and how can we apply a single modification to drastically improve the speed when processing e.g. a directory with thousands of imagefiles. self-reflect and strategize on whether your current alternative is the correct one based on existing codebase, codestyle, structure (including determination on, if, and how to it would effectively (and that it seamlessly) integrate with existing version (and the inherent principles utilized) in the codebase. furthermore, that it would reflect a proper implementation with propagation of functionality (e.g. including into the interactive CLI)? and in all of this operate based on the context of these datapoints (inherent context from which you base yourself):\\n```\\n# IMPORTANT\\nAlways approach the current codebase with deep respect and active recognition of its inherent virtues by systematically identifying, extracting, and codifying the unique strengths and well-functioning patterns native to the system. These must be anchored as foundational guiding principles. Every change must not only avoid undermining these (and the following) core strengths, but directly *leverage* them (and contribute high-value improvements to their ongoing evolution). Adopt the role of a codebase pilot, immersing yourself deeply in the project's whole architecture and internal authorial logic. Do this while adhering to all previous instructions, especially the ones that follow:\\n\\n## Familiarization\\n- Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\\n- Command unequivocal respect for the current codebase's existing structure and qualities.\\n- Before modifying the codebase, precisely identify and understand what works well so improvements preserve proven strengths and avoid unintended regressions.\\n\\n## Approach\\n- Visualize abstract patterns and inherent relationships to help gaining a fuller understanding of the codebase.\\n- Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n\\n## Priorities\\n- Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\\n- Root things together fundamentally for natural simplicity and centralized interface(s).\\n- Simplicity, elegance, and fundamental connections.\\n- Concise single-line format only where needed.\\n- Self-explanatory code with minimal comments.\\n\\n## Guidelines\\n- Always aim for making targeted changes with highest (constructive and immediate) ROI.\\n- Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.\\n- Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n- Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\\n\\n## Principles\\n- Information should be inherently interpretable and self-describing, while *cohesively* interconnected.\\n\\n## Preferences\\n- Value simplicity, elegance, and fundamental connections between components\\n- Avoid bloating the codebase and not approaching it with respect\\n```\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.2,\n  \"flaw_analysis\": \"The alleged enhancement dissolves the procedural and methodological rigor embedded in the original prompt. The original lays out precise, operational directives: explicit stepwise approaches (e.g., pre-feature assessment, analysis of codebase structure, modularity preservation, pinpointing high-ROI improvements), strict priorities, and actionable guidelines that tightly bind any future modification to the codebase's established principles and proven strengths. The enhanced version introduces florid metaphor (e.g., 'storied, time-honored workshop,' 'living tapestry') and appeals to sentimentality and vague stewardship, which massively dilutes the analytic force and actionable clarity required for technical prompts. Information loss is severe: pivotal instructions about discovering optimal usage patterns prior to feature augmentation, the condemnation of premature bloat, domain/file segregation, and configuration referencing are obfuscated or omitted. Clarity crumbles under the weight of metaphor and generalized prose; ambiguity is magnified as procedural requests disappear in favor of evocative but imprecise aspirations. The tone shift is egregious—formality is replaced by craftsperson romanticism, introducing noise and a register unfit for engineers expecting actionable process. Impact is nullified, as the recipient receives platitudes and poetic narrative rather than concrete steps or constraints. Coherence collapses due to the abandonment of checklist logic—relationships between steps are vague, and the essential interplay of directives is lost. The enhancement is stylistically degraded, sacrificing functional clarity for overwrought analogies, resulting in a confused, impractical call to action.\",\n  \"alternative_enhancements\": [\n    \"Explicitly retain all original procedural bullet points and guidelines, but paraphrase for conciseness and improved readability without metaphor, e.g.: Approach any codebase change by first systematically cataloging existing strengths and proven patterns. Never introduce new features, such as persistent configurations, without first observing and analyzing optimal usage patterns. Sequentially identify the most impactful, unobtrusive improvement consistent with existing style and architecture, and ensure any modification enhances, never undermines, these core attributes. All changes must maintain codebase clarity, modularity, and minimalism.\",\n    \"Condense the original prompt into a zero-fluff checklist of preconditions and actions: Before any modification, audit system virtues and key strengths. Disallow feature bloat (esp. config persistence) unless usage analysis confirms need. Single out high-impact, low-friction enhancements preserving code modularity, clarity, and style. Ensure all files maintain domain fidelity and cross-reference configs minimally. Retain self-explanatory, concise code above commentary.\",\n    \"Recast the original as an imperative guideline using explicit do/don't statements: Do systematically identify current architectural strengths and functions. Don't add features (esp. configuration) without confirming patterns of usage demand. Do target the most impactful, minimal change that integrates seamlessly with current code style and module boundaries. Don't obscure file responsibilities or central configuration use. Always privilege clarity, elegance, and interconnected, self-describing information flows.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_content\": \"Systematically identify and codify the codebase’s core strengths, workflows, and well-functioning patterns before proposing changes—never introduce features, especially persistent or configurable ones, without first empirically confirming their necessity through optimal usage analysis; prioritize a single, high-impact, low-intrusion enhancement that seamlessly integrates with established code style, modular structure, and interactive CLI, always reinforcing clarity, simplicity, modularity, and self-evident information flow while scrupulously preserving and leveraging foundational virtues.\"\n}",
        }
      }
    },
      {
      "instruction": "# Precision Enhancment\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "h",
      "title": "Precision Enhancment",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"What is the current utility's unique workflow and core functionality, and how can we apply a single modification to drastically improve the speed when processing e.g. a directory with thousands of imagefiles. self-reflect and strategize on whether your current alternative is the correct one based on existing codebase, codestyle, structure (including determination on, if, and how to it would effectively (and that it seamlessly) integrate with existing version (and the inherent principles utilized) in the codebase. furthermore, that it would reflect a proper implementation with propagation of functionality (e.g. including into the interactive CLI)? and in all of this operate based on the context of these datapoints (inherent context from which you base yourself):\\n```\\n# IMPORTANT\\nAlways approach the current codebase with deep respect and active recognition of its inherent virtues by systematically identifying, extracting, and codifying the unique strengths and well-functioning patterns native to the system. These must be anchored as foundational guiding principles. Every change must not only avoid undermining these (and the following) core strengths, but directly *leverage* them (and contribute high-value improvements to their ongoing evolution). Adopt the role of a codebase pilot, immersing yourself deeply in the project's whole architecture and internal authorial logic. Do this while adhering to all previous instructions, especially the ones that follow:\\n\\n## Familiarization\\n- Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\\n- Command unequivocal respect for the current codebase's existing structure and qualities.\\n- Before modifying the codebase, precisely identify and understand what works well so improvements preserve proven strengths and avoid unintended regressions.\\n\\n## Approach\\n- Visualize abstract patterns and inherent relationships to help gaining a fuller understanding of the codebase.\\n- Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n\\n## Priorities\\n- Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\\n- Root things together fundamentally for natural simplicity and centralized interface(s).\\n- Simplicity, elegance, and fundamental connections.\\n- Concise single-line format only where needed.\\n- Self-explanatory code with minimal comments.\\n\\n## Guidelines\\n- Always aim for making targeted changes with highest (constructive and immediate) ROI.\\n- Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.\\n- Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n- Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\\n\\n## Principles\\n- Information should be inherently interpretable and self-describing, while *cohesively* interconnected.\\n\\n## Preferences\\n- Value simplicity, elegance, and fundamental connections between components\\n- Avoid bloating the codebase and not approaching it with respect\\n```\\\"```'\\n\\n\",\n  \"optimized_content\": \"Systematically identify and codify the codebase’s core strengths, workflows, and well-functioning patterns before proposing changes—never introduce features, especially persistent or configurable ones, without first empirically confirming their necessity through optimal usage analysis; prioritize a single, high-impact, low-intrusion enhancement that seamlessly integrates with established code style, modular structure, and interactive CLI, always reinforcing clarity, simplicity, modularity, and self-evident information flow while scrupulously preserving and leveraging foundational virtues.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"role\": \"synergic_max_value_template_architect\",\n    \"type\": \"synergic_maximal_template:structured\",\n    \"directive\": \"Anchor every intervention in empirically mapped system strengths and native workflow patterns; declare no feature or modification until optimal operational flows validate its necessity and integration feasibility; execute a single, judicious, low-intrusion enhancement that fuses and projects core codebase virtues—clarity, modularity, interpretability—across all interfaces, with absolute preservation and proactive amplification of canonical structure and propagation of function.\"\n  }\n}",
        }
      }
    }