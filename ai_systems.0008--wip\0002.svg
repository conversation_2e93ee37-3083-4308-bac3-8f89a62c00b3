<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0.00000762939453125 0 1016.0520629882812 1246" style="max-width: 1016.0520629882812px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5"><style>#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .error-icon{fill:#a44141;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .edge-thickness-normal{stroke-width:1px;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .marker.cross{stroke:lightgrey;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 p{margin:0;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .cluster-label text{fill:#F9FFFE;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .cluster-label span{color:#F9FFFE;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .cluster-label span p{background-color:transparent;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .label text,#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 span{fill:#ccc;color:#ccc;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .node rect,#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .node circle,#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .node ellipse,#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .node polygon,#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .rough-node .label text,#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .node .label text,#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .image-shape .label,#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .icon-shape .label{text-anchor:middle;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .rough-node .label,#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .node .label,#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .image-shape .label,#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .icon-shape .label{text-align:center;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .node.clickable{cursor:pointer;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .arrowheadPath{fill:lightgrey;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .cluster text{fill:#F9FFFE;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .cluster span{color:#F9FFFE;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 rect.text{fill:none;stroke-width:0;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .icon-shape,#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .icon-shape p,#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .icon-shape rect,#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .activeStage&gt;*{fill:#90EE90!important;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .activeStage span{fill:#90EE90!important;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .emptyStage&gt;*{fill:#FFB6C1!important;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .emptyStage span{fill:#FFB6C1!important;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .coreSystem&gt;*{fill:#87CEEB!important;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .coreSystem span{fill:#87CEEB!important;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .broken&gt;*{stroke-dasharray:5 5!important;}#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5 .broken span{stroke-dasharray:5 5!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RulesForAI_Gen1_0" d="M679.036,86L675.346,90.167C671.657,94.333,664.278,102.667,624.513,114.081C584.749,125.495,512.6,139.99,476.525,147.238L440.45,154.486"></path><path marker-end="url(#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RulesForAI_MDFiles_1" d="M725.758,86L727.06,90.167C728.362,94.333,730.966,102.667,732.268,117.5C733.57,132.333,733.57,153.667,733.57,175C733.57,196.333,733.57,217.667,733.57,239C733.57,260.333,733.57,281.667,733.57,303C733.57,324.333,733.57,345.667,726.973,360.165C720.376,374.664,707.181,382.327,700.584,386.159L693.986,389.991"></path><path marker-end="url(#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Gen1_BaseGen_2" d="M436.529,200.858L460.668,207.215C484.806,213.572,533.084,226.286,557.223,236.143C581.362,246,581.362,253,581.362,256.5L581.362,260"></path><path marker-end="url(#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BaseGen_MDFiles_3" d="M581.362,342L581.362,346.167C581.362,350.333,581.362,358.667,583.732,366.443C586.101,374.219,590.841,381.437,593.21,385.047L595.58,388.656"></path><path marker-end="url(#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Stage1_Gen1_4" d="M328.341,86L328.341,90.167C328.341,94.333,328.341,102.667,328.889,110.341C329.437,118.016,330.534,125.032,331.082,128.54L331.63,132.048"></path><path marker-end="url(#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Stage2_Gen1_5" d="M106.073,1032L105.422,1027.833C104.771,1023.667,103.469,1015.333,102.818,1000.5C102.167,985.667,102.167,964.333,102.167,943C102.167,921.667,102.167,900.333,102.167,879C102.167,857.667,102.167,836.333,102.167,815C102.167,793.667,102.167,772.333,102.167,751C102.167,729.667,102.167,708.333,102.167,687C102.167,665.667,102.167,644.333,102.167,623C102.167,601.667,102.167,580.333,102.167,559C102.167,537.667,102.167,516.333,102.167,495C102.167,473.667,102.167,452.333,102.167,431C102.167,409.667,102.167,388.333,102.167,367C102.167,345.667,102.167,324.333,102.167,303C102.167,281.667,102.167,260.333,124.521,243.609C146.875,226.885,191.584,214.769,213.939,208.711L236.293,202.654"></path><path marker-end="url(#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Stage3_Gen1_6" d="M349.817,1032L347.904,1027.833C345.992,1023.667,342.166,1015.333,340.254,1000.5C338.341,985.667,338.341,964.333,338.341,943C338.341,921.667,338.341,900.333,338.341,879C338.341,857.667,338.341,836.333,338.341,815C338.341,793.667,338.341,772.333,338.341,751C338.341,729.667,338.341,708.333,338.341,687C338.341,665.667,338.341,644.333,338.341,623C338.341,601.667,338.341,580.333,338.341,559C338.341,537.667,338.341,516.333,338.341,495C338.341,473.667,338.341,452.333,338.341,431C338.341,409.667,338.341,388.333,338.341,367C338.341,345.667,338.341,324.333,338.341,303C338.341,281.667,338.341,260.333,338.341,246.167C338.341,232,338.341,225,338.341,221.5L338.341,218"></path><path marker-end="url(#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MDFiles_CatalogMgr_7" d="M623.38,470L623.38,474.167C623.38,478.333,623.38,486.667,623.38,494.333C623.38,502,623.38,509,623.38,512.5L623.38,516"></path><path marker-end="url(#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CatalogMgr_JSONCatalog_8" d="M623.38,598L623.38,602.167C623.38,606.333,623.38,614.667,623.38,622.333C623.38,630,623.38,637,623.38,640.5L623.38,644"></path><path marker-end="url(#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_JSONCatalog_TemplateCatalog_9" d="M623.38,726L623.38,730.167C623.38,734.333,623.38,742.667,623.38,750.333C623.38,758,623.38,765,623.38,768.5L623.38,772"></path><path marker-end="url(#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TemplateCatalog_SeqExecutor_10" d="M623.38,854L623.38,858.167C623.38,862.333,623.38,870.667,623.38,878.333C623.38,886,623.38,893,623.38,896.5L623.38,900"></path><path marker-end="url(#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SeqExecutor_SequenceManager_11" d="M705.486,982L714.257,986.167C723.029,990.333,740.573,998.667,741.817,1006.696C743.062,1014.725,728.006,1022.449,720.478,1026.312L712.951,1030.174"></path><path marker-end="url(#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SequenceManager_SeqExecutor_12" d="M627.286,1032L626.635,1027.833C625.984,1023.667,624.682,1015.333,624.031,1007.667C623.38,1000,623.38,993,623.38,989.5L623.38,986"></path><path marker-end="url(#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SeqExecutor_LLMProviders_13" d="M750.005,971.997L775.48,977.831C800.955,983.665,851.904,995.332,877.379,1004.666C902.854,1014,902.854,1021,902.854,1024.5L902.854,1028"></path><path marker-end="url(#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LLMProviders_OutputFiles_14" d="M902.854,1110L902.854,1114.167C902.854,1118.333,902.854,1126.667,902.854,1134.333C902.854,1142,902.854,1149,902.854,1152.5L902.854,1156"></path><path marker-end="url(#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SeqExecutor_Stage2_15" d="M496.755,963.598L452.286,970.832C407.818,978.065,318.88,992.533,267.33,1003.615C215.779,1014.697,201.615,1022.393,194.533,1026.242L187.451,1030.09"></path><path marker-end="url(#mermaid-2b27fbcb-2d94-4ae9-a52f-1f13c48864d5_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SeqExecutor_Stage3_16" d="M516.832,982L505.448,986.167C494.065,990.333,471.298,998.667,455.176,1006.586C439.054,1014.506,429.577,1022.011,424.838,1025.764L420.1,1029.517"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(713.5703048706055, 47)" id="flowchart-RulesForAI-216" class="node default"><rect height="78" width="240.53125" y="-39" x="-120.265625" style="" class="basic label-container"></rect><g transform="translate(-90.265625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="180.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>RulesForAI.md<br>Template Structure Rules</p></span></div></foreignObject></g></g><g transform="translate(328.3411331176758, 47)" id="flowchart-Stage1-217" class="node default activeStage"><rect height="78" width="200.6875" y="-39" x="-100.34375" style="fill:#90EE90 !important" class="basic label-container"></rect><g transform="translate(-70.34375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="140.6875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Stage1: Prototyping<br>1000-1999 Auto-ID</p></span></div></foreignObject></g></g><g transform="translate(112.16666412353516, 1071)" id="flowchart-Stage2-218" class="node default emptyStage"><rect height="78" width="208.33334350585938" y="-39" x="-104.16667175292969" style="fill:#FFB6C1 !important" class="basic label-container"></rect><g transform="translate(-74.16667175292969, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="148.33334350585938"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Stage2: Validated<br>2000-2999 Manual-ID</p></span></div></foreignObject></g></g><g transform="translate(367.71874237060547, 1071)" id="flowchart-Stage3-219" class="node default emptyStage"><rect height="78" width="202.77084350585938" y="-39" x="-101.38542175292969" style="fill:#FFB6C1 !important" class="basic label-container"></rect><g transform="translate(-71.38542175292969, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="142.77084350585938"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Stage3: Production<br>3000-3999 Stable-ID</p></span></div></foreignObject></g></g><g transform="translate(338.3411331176758, 175)" id="flowchart-Gen1-220" class="node default"><rect height="78" width="196.375" y="-39" x="-98.1875" style="" class="basic label-container"></rect><g transform="translate(-68.1875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="136.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Generator Files<br>*.py in generators/</p></span></div></foreignObject></g></g><g transform="translate(581.3619689941406, 303)" id="flowchart-BaseGen-221" class="node default"><rect height="78" width="234.4166717529297" y="-39" x="-117.20833587646484" style="" class="basic label-container"></rect><g transform="translate(-87.20833587646484, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="174.4166717529297"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>BaseGenerator Class<br>Template Creation Logic</p></span></div></foreignObject></g></g><g transform="translate(623.3801956176758, 431)" id="flowchart-MDFiles-222" class="node default"><rect height="78" width="188.5729217529297" y="-39" x="-94.28646087646484" style="" class="basic label-container"></rect><g transform="translate(-64.28646087646484, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="128.5729217529297"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Template Files<br><em>.md in stage</em>/md/</p></span></div></foreignObject></g></g><g transform="translate(623.3801956176758, 559)" id="flowchart-CatalogMgr-223" class="node default coreSystem"><rect height="78" width="200.3125" y="-39" x="-100.15625" style="fill:#87CEEB !important" class="basic label-container"></rect><g transform="translate(-70.15625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="140.3125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>lvl1_md_to_json.py<br>Catalog Manager</p></span></div></foreignObject></g></g><g transform="translate(623.3801956176758, 687)" id="flowchart-JSONCatalog-224" class="node default"><rect height="78" width="227.09375" y="-39" x="-113.546875" style="" class="basic label-container"></rect><g transform="translate(-83.546875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="167.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>lvl1.md.templates.json<br>Unified Catalog</p></span></div></foreignObject></g></g><g transform="translate(623.3801956176758, 943)" id="flowchart-SeqExecutor-225" class="node default coreSystem"><rect height="78" width="253.25" y="-39" x="-126.625" style="fill:#87CEEB !important" class="basic label-container"></rect><g transform="translate(-96.625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="193.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>lvl1_sequence_executor.py<br>Main Execution Engine</p></span></div></foreignObject></g></g><g transform="translate(623.3801956176758, 815)" id="flowchart-TemplateCatalog-226" class="node default coreSystem"><rect height="78" width="220.9479217529297" y="-39" x="-110.47396087646484" style="fill:#87CEEB !important" class="basic label-container"></rect><g transform="translate(-80.47396087646484, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="160.9479217529297"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>TemplateCatalog Class<br>Multi-Level Loader</p></span></div></foreignObject></g></g><g transform="translate(633.3801956176758, 1071)" id="flowchart-SequenceManager-227" class="node default"><rect height="78" width="228.55209350585938" y="-39" x="-114.27604675292969" style="" class="basic label-container"></rect><g transform="translate(-84.27604675292969, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="168.55209350585938"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>SequenceManager Class<br>Sequence Resolution</p></span></div></foreignObject></g></g><g transform="translate(902.8541488647461, 1071)" id="flowchart-LLMProviders-228" class="node default"><rect height="78" width="210.39584350585938" y="-39" x="-105.19792175292969" style="" class="basic label-container"></rect><g transform="translate(-75.19792175292969, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="150.39584350585938"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>LLM Providers<br>OpenAI, Claude, etc.</p></span></div></foreignObject></g></g><g transform="translate(902.8541488647461, 1199)" id="flowchart-OutputFiles-229" class="node default"><rect height="78" width="188.70834350585938" y="-39" x="-94.35417175292969" style="" class="basic label-container"></rect><g transform="translate(-64.35417175292969, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="128.70834350585938"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>JSON Output Files<br>Execution Results</p></span></div></foreignObject></g></g></g></g></g></svg>