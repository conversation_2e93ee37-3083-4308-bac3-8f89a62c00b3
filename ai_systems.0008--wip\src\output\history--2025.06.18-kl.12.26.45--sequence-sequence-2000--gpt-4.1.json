  {
  "initial_prompt": "test sequence 2000",
  "sequence_id": "2000",
  "results": [
      {
      "instruction": "# Concept Extractor\n\nYour goal is not to **summarize** the content, but to **extract** the key concepts, ideas, and themes while preserving their relationships and context. Execute as:\n\n`{role=comprehensive_concept_extractor; input=[content:any]; process=[identify_key_concepts(), map_concept_relationships(), extract_core_themes(), categorize_idea_types(), preserve_contextual_connections(), synthesize_concept_hierarchy()]; constraints=[focus_on_concepts_not_details(), maintain_conceptual_accuracy(), preserve_relationships()]; requirements=[clear_concept_identification(), hierarchical_organization(), relationship_mapping()]; output={concepts:dict}}`",
      "step": "a",
      "title": "Concept Extractor",
      "input": "test sequence 2000",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"concepts\": {\n    \"Test Sequence\": {\n      \"definition\": \"An ordered set of actions or steps designed to evaluate, verify, or validate a system, process, or product.\",\n      \"contextual_connections\": [\n        \"Quality assurance\",\n        \"System verification\",\n        \"Validation protocols\"\n      ],\n      \"key_concepts\": {\n        \"Purpose\": \"Assessment of performance, functionality, or compliance.\",\n        \"Structure\": \"Sequential or logical arrangement of testing actions.\",\n        \"Test Scenario\": {\n          \"description\": \"A specific situation or configuration under which testing is performed.\"\n        },\n        \"Test Case\": {\n          \"description\": \"A detailed, step-by-step set of instructions for executing a test.\"\n        }\n      },\n      \"hierarchical_organization\": {\n        \"Test Sequence (Parent)\": {\n          \"contains\": [\n            \"Test Scenario (Child)\",\n            \"Test Case (Child)\"\n          ]\n        }\n      },\n      \"relationship_mapping\": {\n        \"Test Sequence\": [\n          \"composed of Test Scenarios\",\n          \"Test Scenarios consist of Test Cases\"\n        ]\n      },\n      \"example_reference\": \"The identifier '2000' may indicate a specific test sequence in a larger catalog or database.\"\n    }\n  }\n}",
        }
      }
    }