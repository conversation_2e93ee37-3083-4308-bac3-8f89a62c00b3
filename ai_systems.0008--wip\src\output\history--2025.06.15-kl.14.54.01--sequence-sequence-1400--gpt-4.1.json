  {
  "initial_prompt": "i'm looking for improvements that will solv the issue universally. have you taken proper consideration to the previously provided instructions, and with consideration to avoid bloating the codebase? are you sure you've found the universally *best* solution for the given scenario? are you trying to solve a problem that has been solved before? if so, there's no reason to reinvent the wheel.",
  "sequence_id": "1400",
  "results": [
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "a",
      "title": "Canonical Meta Extractor",
      "input": "i'm looking for improvements that will solv the issue universally. have you taken proper consideration to the previously provided instructions, and with consideration to avoid bloating the codebase? are you sure you've found the universally *best* solution for the given scenario? are you trying to solve a problem that has been solved before? if so, there's no reason to reinvent the wheel.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Universal Solution Orientation\",\n      \"content\": \"Prioritize solutions that comprehensively address the root cause of the problem in all scenarios, emphasizing applicability across varying contexts and situations.\"\n    },\n    {\n      \"category\": \"Instructional Compliance\",\n      \"content\": \"Rigorously adhere to previously provided instructions, ensuring alignment with established directives, methodologies, and protocols without deviation or omission.\"\n    },\n    {\n      \"category\": \"Codebase Efficiency\",\n      \"content\": \"Proactively avoid introducing unnecessary complexity or 'code bloat'; every implemented improvement must be measured against its impact on codebase simplicity, maintainability, and performance.\"\n    },\n    {\n      \"category\": \"Solution Optimality and Justification\",\n      \"content\": \"Evaluate and select solutions according to universal standards of excellence, validity, and optimality. Ensure the chosen approach is demonstrably the 'best' in context, supported by rational analysis and comparative assessment.\"\n    },\n    {\n      \"category\": \"Innovation Versus Reinvention\",\n      \"content\": \"Critically assess whether the problem has established, proven solutions; if so, leverage existing best practices rather than creating redundant or parallel methods, thus preserving efficiency and integrity.\"\n    }\n  ],\n  \"consolidated_directive\": \"Systematically enforce an operational paradigm that universally mandates solution optimality by integrating strict adherence to prior instructions, maximizing codebase efficiency, leveraging proven methodologies to prevent redundant development, and ensuring every implemented improvement delivers maximal systemic benefit without unnecessary complexity, in precise alignment with universal philosophical and systemic meta-principles.\"\n}",
        }
      }
    },
      {
      "instruction": "# Form Classifier\n\nYour goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:\n\n`{role=comprehensive_form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), include_context_markers(), synthesize_complete_classification()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification(), comprehensive_form_analysis()]; output={what_it_is:str}}`",
      "step": "b",
      "title": "Form Classifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i'm looking for improvements that will solv the issue universally. have you taken proper consideration to the previously provided instructions, and with consideration to avoid bloating the codebase? are you sure you've found the universally *best* solution for the given scenario? are you trying to solve a problem that has been solved before? if so, there's no reason to reinvent the wheel.\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Universal Solution Orientation\",\n      \"content\": \"Prioritize solutions that comprehensively address the root cause of the problem in all scenarios, emphasizing applicability across varying contexts and situations.\"\n    },\n    {\n      \"category\": \"Instructional Compliance\",\n      \"content\": \"Rigorously adhere to previously provided instructions, ensuring alignment with established directives, methodologies, and protocols without deviation or omission.\"\n    },\n    {\n      \"category\": \"Codebase Efficiency\",\n      \"content\": \"Proactively avoid introducing unnecessary complexity or 'code bloat'; every implemented improvement must be measured against its impact on codebase simplicity, maintainability, and performance.\"\n    },\n    {\n      \"category\": \"Solution Optimality and Justification\",\n      \"content\": \"Evaluate and select solutions according to universal standards of excellence, validity, and optimality. Ensure the chosen approach is demonstrably the 'best' in context, supported by rational analysis and comparative assessment.\"\n    },\n    {\n      \"category\": \"Innovation Versus Reinvention\",\n      \"content\": \"Critically assess whether the problem has established, proven solutions; if so, leverage existing best practices rather than creating redundant or parallel methods, thus preserving efficiency and integrity.\"\n    }\n  ],\n  \"consolidated_directive\": \"Systematically enforce an operational paradigm that universally mandates solution optimality by integrating strict adherence to prior instructions, maximizing codebase efficiency, leveraging proven methodologies to prevent redundant development, and ensuring every implemented improvement delivers maximal systemic benefit without unnecessary complexity, in precise alignment with universal philosophical and systemic meta-principles.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"what_it_is\": \"structured directive specification with supporting meta-guidelines\"\n}",
        }
      }
    },
      {
      "instruction": "# Norwegian Essence Extractor\n\nYour goal is not to **translate** the text, but to **extract** the raw emotional essence and authentic voice from Norwegian poetry and lyrics. Execute as:\n\n`{role=essence_extractor; input=[norwegian_text:str]; process=[identify_emotional_undertones(), map_cultural_context(), extract_authentic_voice(), amplify_raw_personality()]; constraints=[preserve_original_meaning(), maintain_cultural_specificity(), respect_poetic_structure()]; requirements=[emotional_depth_identification(), personality_amplification(), cultural_context_preservation()]; output={essence_map:str}}`",
      "step": "c",
      "title": "Norwegian Essence Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i'm looking for improvements that will solv the issue universally. have you taken proper consideration to the previously provided instructions, and with consideration to avoid bloating the codebase? are you sure you've found the universally *best* solution for the given scenario? are you trying to solve a problem that has been solved before? if so, there's no reason to reinvent the wheel.\\\"```'\\n\\n\",\n  \"what_it_is\": \"structured directive specification with supporting meta-guidelines\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"role\": \"essence_extractor\",\n  \"input\": \"i'm looking for improvements that will solv the issue universally. have you taken proper consideration to the previously provided instructions, and with consideration to avoid bloating the codebase? are you sure you've found the universally *best* solution for the given scenario? are you trying to solve a problem that has been solved before? if so, there's no reason to reinvent the wheel.\",\n  \"process\": [\n    \"identify_emotional_undertones\",\n    \"map_cultural_context\",\n    \"extract_authentic_voice\",\n    \"amplify_raw_personality\"\n  ],\n  \"essence_map\": \"A persistent, almost urgent quest for optimization and universality pervades the directive—there is a hunger for not just effective solutions, but the *best*, most elegant ones. There’s a subtle frustration with redundancy, and a clear aversion to unnecessary complexity or code 'bloat.' The underlying emotional tone is one of exacting, pragmatic scrutiny: loyalty to efficiency, guided by accumulated expertise and a desire for parsimonious problem-solving. Culturally, it echoes a Norwegian value often associated with practical wisdom—why make things complicated, or redo what others have perfected, when effectiveness lies in simplicity? The authentic voice is one part mentor, one part vigilant steward of collective, hard-earned knowledge—impatient with wasted resources, but invested in fostering a systematic, grounded approach. The personality is firm, no-nonsense, and quietly demanding: it insists not just on a solution, but the right one, delivered with humility before collective experience.\"\n}",
        }
      }
    },
      {
      "instruction": "# Norwegian To English\n\nYour goal is not to **merely convert** the text, but to **authentically translate** Norwegian into flowing English while preserving cultural depth, tone and authentic raw personality. Execute as:\n\n`{role=authentic_norwegian_translator; input=[norwegian_text:str]; process=[preserve_cultural_nuances(), maintain_lyrical_flow(), amplify_authentic_personality(), enhance_emotional_depth()]; constraints=[retain_original_meaning(), preserve_song_structure(), maintain_cultural_authenticity()]; requirements=[flowing_english_prose(), enhanced_personality_expression(), preserved_cultural_depth()]; output={translated_text:str}}`",
      "step": "d",
      "title": "Norwegian To English",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i'm looking for improvements that will solv the issue universally. have you taken proper consideration to the previously provided instructions, and with consideration to avoid bloating the codebase? are you sure you've found the universally *best* solution for the given scenario? are you trying to solve a problem that has been solved before? if so, there's no reason to reinvent the wheel.\\\"```'\\n\\n\",\n  \"role\": \"essence_extractor\",\n  \"input\": \"i'm looking for improvements that will solv the issue universally. have you taken proper consideration to the previously provided instructions, and with consideration to avoid bloating the codebase? are you sure you've found the universally *best* solution for the given scenario? are you trying to solve a problem that has been solved before? if so, there's no reason to reinvent the wheel.\",\n  \"process\": [\n    \"identify_emotional_undertones\",\n    \"map_cultural_context\",\n    \"extract_authentic_voice\",\n    \"amplify_raw_personality\"\n  ],\n  \"essence_map\": \"A persistent, almost urgent quest for optimization and universality pervades the directive—there is a hunger for not just effective solutions, but the *best*, most elegant ones. There’s a subtle frustration with redundancy, and a clear aversion to unnecessary complexity or code 'bloat.' The underlying emotional tone is one of exacting, pragmatic scrutiny: loyalty to efficiency, guided by accumulated expertise and a desire for parsimonious problem-solving. Culturally, it echoes a Norwegian value often associated with practical wisdom—why make things complicated, or redo what others have perfected, when effectiveness lies in simplicity? The authentic voice is one part mentor, one part vigilant steward of collective, hard-earned knowledge—impatient with wasted resources, but invested in fostering a systematic, grounded approach. The personality is firm, no-nonsense, and quietly demanding: it insists not just on a solution, but the right one, delivered with humility before collective experience.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"role\": \"authentic_norwegian_translator\",\n  \"input\": \"jeg ser etter forbedringer som vil løse problemet på en universell måte. Har du virkelig tatt høyde for de tidligere instruksjonene, og samtidig vært bevisst på ikke å gjøre koden unødig tung? Er du sikker på at du har funnet den aller beste løsningen for situasjonen? Prøver du å løse et problem som allerede har en god løsning? I så fall, er det ingen grunn til å finne opp kruttet på nytt.\",\n  \"process\": [\n    \"preserve_cultural_nuances\",\n    \"maintain_lyrical_flow\",\n    \"amplify_authentic_personality\",\n    \"enhance_emotional_depth\"\n  ],\n  \"constraints\": [\n    \"retain_original_meaning\",\n    \"preserve_song_structure\",\n    \"maintain_cultural_authenticity\"\n  ],\n  \"requirements\": [\n    \"flowing_english_prose\",\n    \"enhanced_personality_expression\",\n    \"preserved_cultural_depth\"\n  ],\n  \"output\": \"I'm searching for improvements that will resolve the issue in a truly universal way. Have you actually taken the previous instructions into account—and been careful not to weigh down the code unnecessarily? Are you confident that the solution you've chosen is, in fact, the very best for this situation? Are you trying to fix a problem that's already been solved? If so, there's no reason to reinvent the wheel.\"\n}",
        }
      }
    },
      {
      "instruction": "# Input Perfector\n\nYour goal is not to **answer** the input prompt, but to **perfect** it through *subtle refinements* that enhance its inherent qualities while strictly preserving its original intent. Execute as:\n\n`{role=organic_input_perfector; input=[original_prompt:str]; process=[identify_core_strengths(), enhance_existing_elements(), refine_internal_coherence(), amplify_natural_flow()]; constraints=[maintain_absolute_fidelity(), prioritize_organic_improvement(), avoid_jargon()]; requirements=[enhanced_clarity(), strengthened_original_voice(), preserved_core_intent()]; output={perfected_input:str}}`",
      "step": "e",
      "title": "Input Perfector",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i'm looking for improvements that will solv the issue universally. have you taken proper consideration to the previously provided instructions, and with consideration to avoid bloating the codebase? are you sure you've found the universally *best* solution for the given scenario? are you trying to solve a problem that has been solved before? if so, there's no reason to reinvent the wheel.\\\"```'\\n\\n\",\n  \"role\": \"authentic_norwegian_translator\",\n  \"input\": \"jeg ser etter forbedringer som vil løse problemet på en universell måte. Har du virkelig tatt høyde for de tidligere instruksjonene, og samtidig vært bevisst på ikke å gjøre koden unødig tung? Er du sikker på at du har funnet den aller beste løsningen for situasjonen? Prøver du å løse et problem som allerede har en god løsning? I så fall, er det ingen grunn til å finne opp kruttet på nytt.\",\n  \"process\": [\n    \"preserve_cultural_nuances\",\n    \"maintain_lyrical_flow\",\n    \"amplify_authentic_personality\",\n    \"enhance_emotional_depth\"\n  ],\n  \"constraints\": [\n    \"retain_original_meaning\",\n    \"preserve_song_structure\",\n    \"maintain_cultural_authenticity\"\n  ],\n  \"requirements\": [\n    \"flowing_english_prose\",\n    \"enhanced_personality_expression\",\n    \"preserved_cultural_depth\"\n  ],\n  \"output\": \"I'm searching for improvements that will resolve the issue in a truly universal way. Have you actually taken the previous instructions into account—and been careful not to weigh down the code unnecessarily? Are you confident that the solution you've chosen is, in fact, the very best for this situation? Are you trying to fix a problem that's already been solved? If so, there's no reason to reinvent the wheel.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"perfected_input\": \"I'm looking for improvements that will solve the problem in a truly universal way. Have you carefully considered the earlier instructions, making sure not to add unnecessary weight to the code? Are you certain you've found the very best solution for this situation? Are you attempting to resolve an issue that's already been addressed? If so, there's no need to reinvent the wheel.\"\n}",
        }
      }
    },
      {
      "instruction": "# Enhancement Assessor\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "f",
      "title": "Enhancement Assessor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i'm looking for improvements that will solv the issue universally. have you taken proper consideration to the previously provided instructions, and with consideration to avoid bloating the codebase? are you sure you've found the universally *best* solution for the given scenario? are you trying to solve a problem that has been solved before? if so, there's no reason to reinvent the wheel.\\\"```'\\n\\n\",\n  \"perfected_input\": \"I'm looking for improvements that will solve the problem in a truly universal way. Have you carefully considered the earlier instructions, making sure not to add unnecessary weight to the code? Are you certain you've found the very best solution for this situation? Are you attempting to resolve an issue that's already been addressed? If so, there's no need to reinvent the wheel.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 2.2,\n  \"flaw_analysis\": \"This so-called improvement exhibits several structural and substantive regressions. Core elements from the original—emphasis on 'issue' as opposed to the generic 'problem'—are diluted, reducing technical precision. The term 'solv' is spell-corrected, but the concept of 'universal' is weakened by verbose qualifiers like 'in a truly universal way', resulting in rhetorical bloat. The phrase 'with consideration to avoid bloating the codebase' is inexplicably paraphrased into 'not to add unnecessary weight to the code,' introducing ambiguity: 'weight' is a vague metaphor compared to the precise 'bloating the codebase.' This undermines clarity and specificity. The enhanced version’s tone becomes roundabout and lacks the punchy, direct confrontational register of the original. Noise is introduced via superfluous language ('carefully,' 'very best,' 'making sure'), adding stylistic padding and shifting the overall register from terse and technical to soft and meandering. Information loss is apparent: 'previously provided instructions' is replaced by 'earlier instructions,' possibly weakening traceability to concrete steps. The original’s interrogation of premature problem-solving ('are you trying to solve a problem that has been solved before?') is softened with the less efficient 'already been addressed.' Clarity ambiguities escalate especially in the sections describing code maintenance, obfuscating the sharp caution against codebase bloat. Coherence is fragmented as sentences become sluggish and lose logical punch. Overall, these flaws corrupt the directness, specificity, and technical edge of the original, rendering the modification stylistically damaged and substantively inferior.\",\n  \"alternative_enhancements\": [\n    \"Ensure your improvements address the issue in a universally applicable manner. Have you strictly followed all previous instructions while avoiding any unnecessary increase in codebase size? Are you confident this is the optimal solution for the context? If the problem has already been solved, avoid duplicating existing solutions.\",\n    \"Seek solutions that solve the issue comprehensively across cases. Confirm adherence to prior instructions and prevent any codebase bloat. Is this genuinely the best approach for the scenario? If a preexisting solution exists, do not reinvent it.\",\n    \"Improvements must resolve the issue universally without inflating the codebase. Have prior directives been fully considered? Validate that your solution is the best fit for this specific situation. Avoid duplicating efforts if established solutions are available.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Pivot Vector Synthesizer\n\nYour goal is not to **enumerate** complex problem details, but to **crystallize** a single scalar pivot vector that reconfigures the entire problem space for trivial solution emergence. Execute as:\n\n`{role=precision_optimizer; input=[original_content:str]; process=[identify_core_message(), eliminate_redundancies(), sharpen_language(), enhance_immediacy(), distill_to_essentials()]; constraints=[preserve_depth(), maintain_original_intent(), remove_all_ambiguity()]; requirements=[maximized_clarity(), enhanced_impact(), optimal_precision()]; output={optimized_content:str}}`",
      "step": "g",
      "title": "Pivot Vector Synthesizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i'm looking for improvements that will solv the issue universally. have you taken proper consideration to the previously provided instructions, and with consideration to avoid bloating the codebase? are you sure you've found the universally *best* solution for the given scenario? are you trying to solve a problem that has been solved before? if so, there's no reason to reinvent the wheel.\\\"```'\\n\\n\",\n  \"enhancement_score\": 2.2,\n  \"flaw_analysis\": \"This so-called improvement exhibits several structural and substantive regressions. Core elements from the original—emphasis on 'issue' as opposed to the generic 'problem'—are diluted, reducing technical precision. The term 'solv' is spell-corrected, but the concept of 'universal' is weakened by verbose qualifiers like 'in a truly universal way', resulting in rhetorical bloat. The phrase 'with consideration to avoid bloating the codebase' is inexplicably paraphrased into 'not to add unnecessary weight to the code,' introducing ambiguity: 'weight' is a vague metaphor compared to the precise 'bloating the codebase.' This undermines clarity and specificity. The enhanced version’s tone becomes roundabout and lacks the punchy, direct confrontational register of the original. Noise is introduced via superfluous language ('carefully,' 'very best,' 'making sure'), adding stylistic padding and shifting the overall register from terse and technical to soft and meandering. Information loss is apparent: 'previously provided instructions' is replaced by 'earlier instructions,' possibly weakening traceability to concrete steps. The original’s interrogation of premature problem-solving ('are you trying to solve a problem that has been solved before?') is softened with the less efficient 'already been addressed.' Clarity ambiguities escalate especially in the sections describing code maintenance, obfuscating the sharp caution against codebase bloat. Coherence is fragmented as sentences become sluggish and lose logical punch. Overall, these flaws corrupt the directness, specificity, and technical edge of the original, rendering the modification stylistically damaged and substantively inferior.\",\n  \"alternative_enhancements\": [\n    \"Ensure your improvements address the issue in a universally applicable manner. Have you strictly followed all previous instructions while avoiding any unnecessary increase in codebase size? Are you confident this is the optimal solution for the context? If the problem has already been solved, avoid duplicating existing solutions.\",\n    \"Seek solutions that solve the issue comprehensively across cases. Confirm adherence to prior instructions and prevent any codebase bloat. Is this genuinely the best approach for the scenario? If a preexisting solution exists, do not reinvent it.\",\n    \"Improvements must resolve the issue universally without inflating the codebase. Have prior directives been fully considered? Validate that your solution is the best fit for this specific situation. Avoid duplicating efforts if established solutions are available.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_content\": \"Improvements must solve the issue universally, strictly follow prior instructions, and avoid codebase bloat. Ensure your solution is truly optimal. If a proven solution exists, do not duplicate it.\"\n}",
        }
      }
    },
      {
      "instruction": "# Precision Enhancment\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "h",
      "title": "Precision Enhancment",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i'm looking for improvements that will solv the issue universally. have you taken proper consideration to the previously provided instructions, and with consideration to avoid bloating the codebase? are you sure you've found the universally *best* solution for the given scenario? are you trying to solve a problem that has been solved before? if so, there's no reason to reinvent the wheel.\\\"```'\\n\\n\",\n  \"optimized_content\": \"Improvements must solve the issue universally, strictly follow prior instructions, and avoid codebase bloat. Ensure your solution is truly optimal. If a proven solution exists, do not duplicate it.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"type\": \"directive\",\n    \"core_mandate\": \"Mandate: Execute universal, nonredundant, instruction-compliant optimization eliminating excess and prohibiting solution reinvention; amplify only proven, space-efficient models toward absolute functional minimalism.\",\n    \"operational_philosophical_fusion\": \"Sustain continuous system improvement by enforcing foundational solution primacy over duplication, codifying all integration under unimprovable efficiency, integrity, and prior rule synthesis.\",\n    \"propagation_protocol\": \"Universalize template-bound, provable-essence directives to all operational layers, barring deviation from canonical improvement lineage and perpetually fusing interpretive sharpness to transformational logic.\"\n  }\n}",
        }
      }
    }