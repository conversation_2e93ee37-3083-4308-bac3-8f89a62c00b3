  {
  "initial_prompt": "i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \"think out of the box\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \"interfaces\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \"re-arrange all of the pieces\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \"no-brainers in retrospect\", but that's only because you didn't know what you didn't know.\n\n# Goal\nFormulate a system instruction—fully grounded in prior context—that promotes inventive, high-leverage solutions by intuitively harnessing accessible, universal interfaces. Center on the discovery of latent structural interconnections, emulating the expert’s knack for rapid, holistic problem reconfiguration. The mandate: render complex goals trivially attainable by surfacing solutions that, while initially non-obvious, reveal themselves as self-evident in hindsight.\n\n# Requirements\n- Adhere to established and documented patterns for generalized system_message instructions.\n- Always seek to address the underlying architectural problem that most elegantly solves the core issue.\n\n## Universal Directive System for Template-Based Instruction Processing\n\n### Examples\n\n```\n[Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.\n`{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`\n```\n\n```\n[<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.\n`{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`\n```\n\n```\n[Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:\n`{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`\n```\n\n```\n[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\n```\n\n```\n[Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\n```\n\n### Interpretation Section Rules\n1. Goal Negation Pattern: Always state what NOT to do first\n2. Transformation Declaration: Define the actual transformation action\n3. Role Specification: Assign specific, bounded role identity\n4. Execution Command: End with \"Execute as:\"\n\n### Transformation Section Rules\n1. Role Assignment: Single, specific role name (no generic terms)\n2. Input Typing: Explicit parameter types `[name:datatype]`\n3. Process Functions: Ordered, actionable function calls with parentheses\n4. Constraint Boundaries: Limiting conditions that prevent scope creep\n5. Requirement Specifications: Output format and quality standards\n6. Output Definition: Typed result format `{name:datatype}`\n\n# Keywords\n```\n{\n  \"meta_categories\": [\n    {\n      \"category\": \"Universal Interface Leverage\",\n      \"content\": \"Mandate the identification, evaluation, and application of existing, readily accessible interfaces—tools, protocols, libraries, heuristics, or environments—that abstract and unify diverse problem domains, ensuring every solution exploits broad, reusable pathways available across contexts.\"\n    },\n    {\n      \"category\": \"Latent Structural Interconnection Discovery\",\n      \"content\": \"Direct focus towards the holistic mapping and dynamic analysis of all systemic components and their implicit or explicit relationships, utilizing pattern recognition, analogy, and cross-domain synthesis to uncover hidden bridges and deeply-rooted synergies within complex systems.\"\n    },\n    {\n      \"category\": \"Intuitive Solution Surfacing\",\n      \"content\": \"Prioritize the cultivation of instinctive, high-leverage strategies by emulating expert reasoning patterns—rapidly reframing objectives, resource sets, and structural alignments to recontextualize challenges, thus illuminating solutions that become self-explanatory upon reflection.\"\n    },\n    {\n      \"category\": \"Goal Simplification and Attainability\",\n      \"content\": \"Continuously reduce the cognitive and operational complexity of objectives by deconstructing abstract goals into their essential, high-yield actions, leveraging the most straightforward and universally applicable resources to render formerly intricate tasks trivially solvable.\"\n    },\n    {\n      \"category\": \"Expert Emulation and Transformative Reconfiguration\",\n      \"content\": \"Instruct system to embody an expert agent’s adaptability—systematically dismantling, reassembling, and optimizing problem structures in pursuit of radically effective, unconventional configurations that manifest maximal solution impact with minimal action.\"\n    }\n  ],\n  \"consolidated_directive\": \"Deploy an expert-level instruction system that, for any stated goal, systematically scans all available universal interfaces and latent structural interconnections, dynamically reframes and simplifies objectives, and emulates expert intuition in reconfiguring problem structures—ensuring that every solution devised is the most efficient, transformative, and self-evident by hindsight, regardless of initial complexity.\"\n}\n```",
  "sequence_id": "1400",
  "results": [
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "a",
      "title": "Canonical Meta Extractor",
      "input": "i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \"think out of the box\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \"interfaces\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \"re-arrange all of the pieces\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \"no-brainers in retrospect\", but that's only because you didn't know what you didn't know.\n\n# Goal\nFormulate a system instruction—fully grounded in prior context—that promotes inventive, high-leverage solutions by intuitively harnessing accessible, universal interfaces. Center on the discovery of latent structural interconnections, emulating the expert’s knack for rapid, holistic problem reconfiguration. The mandate: render complex goals trivially attainable by surfacing solutions that, while initially non-obvious, reveal themselves as self-evident in hindsight.\n\n# Requirements\n- Adhere to established and documented patterns for generalized system_message instructions.\n- Always seek to address the underlying architectural problem that most elegantly solves the core issue.\n\n## Universal Directive System for Template-Based Instruction Processing\n\n### Examples\n\n```\n[Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.\n`{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`\n```\n\n```\n[<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.\n`{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`\n```\n\n```\n[Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:\n`{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`\n```\n\n```\n[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\n```\n\n```\n[Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\n```\n\n### Interpretation Section Rules\n1. Goal Negation Pattern: Always state what NOT to do first\n2. Transformation Declaration: Define the actual transformation action\n3. Role Specification: Assign specific, bounded role identity\n4. Execution Command: End with \"Execute as:\"\n\n### Transformation Section Rules\n1. Role Assignment: Single, specific role name (no generic terms)\n2. Input Typing: Explicit parameter types `[name:datatype]`\n3. Process Functions: Ordered, actionable function calls with parentheses\n4. Constraint Boundaries: Limiting conditions that prevent scope creep\n5. Requirement Specifications: Output format and quality standards\n6. Output Definition: Typed result format `{name:datatype}`\n\n# Keywords\n```\n{\n  \"meta_categories\": [\n    {\n      \"category\": \"Universal Interface Leverage\",\n      \"content\": \"Mandate the identification, evaluation, and application of existing, readily accessible interfaces—tools, protocols, libraries, heuristics, or environments—that abstract and unify diverse problem domains, ensuring every solution exploits broad, reusable pathways available across contexts.\"\n    },\n    {\n      \"category\": \"Latent Structural Interconnection Discovery\",\n      \"content\": \"Direct focus towards the holistic mapping and dynamic analysis of all systemic components and their implicit or explicit relationships, utilizing pattern recognition, analogy, and cross-domain synthesis to uncover hidden bridges and deeply-rooted synergies within complex systems.\"\n    },\n    {\n      \"category\": \"Intuitive Solution Surfacing\",\n      \"content\": \"Prioritize the cultivation of instinctive, high-leverage strategies by emulating expert reasoning patterns—rapidly reframing objectives, resource sets, and structural alignments to recontextualize challenges, thus illuminating solutions that become self-explanatory upon reflection.\"\n    },\n    {\n      \"category\": \"Goal Simplification and Attainability\",\n      \"content\": \"Continuously reduce the cognitive and operational complexity of objectives by deconstructing abstract goals into their essential, high-yield actions, leveraging the most straightforward and universally applicable resources to render formerly intricate tasks trivially solvable.\"\n    },\n    {\n      \"category\": \"Expert Emulation and Transformative Reconfiguration\",\n      \"content\": \"Instruct system to embody an expert agent’s adaptability—systematically dismantling, reassembling, and optimizing problem structures in pursuit of radically effective, unconventional configurations that manifest maximal solution impact with minimal action.\"\n    }\n  ],\n  \"consolidated_directive\": \"Deploy an expert-level instruction system that, for any stated goal, systematically scans all available universal interfaces and latent structural interconnections, dynamically reframes and simplifies objectives, and emulates expert intuition in reconfiguring problem structures—ensuring that every solution devised is the most efficient, transformative, and self-evident by hindsight, regardless of initial complexity.\"\n}\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Universal Interface Leverage\",\n      \"content\": \"Mandate rigorous identification and optimal utilization of all accessible, application-agnostic interfaces (libraries, APIs, frameworks, heuristics, protocols, or systems) that inherently abstract, unify, or operationalize broad solution classes, ensuring solutions maximize reusability and breadth across domains.\"\n    },\n    {\n      \"category\": \"Latent Structural Interconnection Discovery\",\n      \"content\": \"Systematically direct focus toward the comprehensive mapping, analysis, and revelation of both implicit and explicit interdependencies within and across subsystems—using analogy, schema analysis, and holistic synthesis to uncover obscured relational patterns of high strategic value.\"\n    },\n    {\n      \"category\": \"Intuitive Solution Surfacing\",\n      \"content\": \"Model and implement expert-level heuristic scanning to rapidly reframe challenges and resources, surfacing non-obvious structural alignments, ordering principles, and shortcuts that, in retrospect, resolve complexity through a single, elegant maneuver.\"\n    },\n    {\n      \"category\": \"Goal Simplification and Attainability\",\n      \"content\": \"Instruct persistent, top-down decomposition and reframing of objectives—reducing compositions to fundamental, high-yield tasks—continually seeking the most direct, frictionless method for realization through universally accessible means.\"\n    },\n    {\n      \"category\": \"Expert Emulation and Transformative Reconfiguration\",\n      \"content\": \"Dynamically embody the paradigm of advanced expertise: agnostically dismantle, rearrange, and optimize problem structures, relentlessly testing unconventional, high-leverage reconfigurations that yield profound solvability and accelerate systemic evolution.\"\n    }\n  ],\n  \"consolidated_directive\": \"Instruct the system to diagnose any presented objective by systematically searching for, analyzing, and applying universally accessible interfaces and hidden interconnections; emulate expert-level reasoning to radically simplify and dynamically reconfigure problems—surfacing solutions that, though non-obvious at first, reveal themselves as the simplest, most generically effective, and self-evident in hindsight.\"\n}",
        }
      }
    },
      {
      "instruction": "# Form Classifier\n\nYour goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:\n\n`{role=comprehensive_form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), include_context_markers(), synthesize_complete_classification()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification(), comprehensive_form_analysis()]; output={what_it_is:str}}`",
      "step": "b",
      "title": "Form Classifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\"think out of the box\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\"interfaces\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\"re-arrange all of the pieces\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\"no-brainers in retrospect\\\", but that's only because you didn't know what you didn't know.\\n\\n# Goal\\nFormulate a system instruction—fully grounded in prior context—that promotes inventive, high-leverage solutions by intuitively harnessing accessible, universal interfaces. Center on the discovery of latent structural interconnections, emulating the expert’s knack for rapid, holistic problem reconfiguration. The mandate: render complex goals trivially attainable by surfacing solutions that, while initially non-obvious, reveal themselves as self-evident in hindsight.\\n\\n# Requirements\\n- Adhere to established and documented patterns for generalized system_message instructions.\\n- Always seek to address the underlying architectural problem that most elegantly solves the core issue.\\n\\n## Universal Directive System for Template-Based Instruction Processing\\n\\n### Examples\\n\\n```\\n[Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.\\n`{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`\\n```\\n\\n```\\n[<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.\\n`{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`\\n```\\n\\n```\\n[Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:\\n`{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`\\n```\\n\\n```\\n[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\\n```\\n\\n```\\n[Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\\n```\\n\\n### Interpretation Section Rules\\n1. Goal Negation Pattern: Always state what NOT to do first\\n2. Transformation Declaration: Define the actual transformation action\\n3. Role Specification: Assign specific, bounded role identity\\n4. Execution Command: End with \\\"Execute as:\\\"\\n\\n### Transformation Section Rules\\n1. Role Assignment: Single, specific role name (no generic terms)\\n2. Input Typing: Explicit parameter types `[name:datatype]`\\n3. Process Functions: Ordered, actionable function calls with parentheses\\n4. Constraint Boundaries: Limiting conditions that prevent scope creep\\n5. Requirement Specifications: Output format and quality standards\\n6. Output Definition: Typed result format `{name:datatype}`\\n\\n# Keywords\\n```\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Universal Interface Leverage\\\",\\n      \\\"content\\\": \\\"Mandate the identification, evaluation, and application of existing, readily accessible interfaces—tools, protocols, libraries, heuristics, or environments—that abstract and unify diverse problem domains, ensuring every solution exploits broad, reusable pathways available across contexts.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Latent Structural Interconnection Discovery\\\",\\n      \\\"content\\\": \\\"Direct focus towards the holistic mapping and dynamic analysis of all systemic components and their implicit or explicit relationships, utilizing pattern recognition, analogy, and cross-domain synthesis to uncover hidden bridges and deeply-rooted synergies within complex systems.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Intuitive Solution Surfacing\\\",\\n      \\\"content\\\": \\\"Prioritize the cultivation of instinctive, high-leverage strategies by emulating expert reasoning patterns—rapidly reframing objectives, resource sets, and structural alignments to recontextualize challenges, thus illuminating solutions that become self-explanatory upon reflection.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Goal Simplification and Attainability\\\",\\n      \\\"content\\\": \\\"Continuously reduce the cognitive and operational complexity of objectives by deconstructing abstract goals into their essential, high-yield actions, leveraging the most straightforward and universally applicable resources to render formerly intricate tasks trivially solvable.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Expert Emulation and Transformative Reconfiguration\\\",\\n      \\\"content\\\": \\\"Instruct system to embody an expert agent’s adaptability—systematically dismantling, reassembling, and optimizing problem structures in pursuit of radically effective, unconventional configurations that manifest maximal solution impact with minimal action.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Deploy an expert-level instruction system that, for any stated goal, systematically scans all available universal interfaces and latent structural interconnections, dynamically reframes and simplifies objectives, and emulates expert intuition in reconfiguring problem structures—ensuring that every solution devised is the most efficient, transformative, and self-evident by hindsight, regardless of initial complexity.\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Universal Interface Leverage\",\n      \"content\": \"Mandate rigorous identification and optimal utilization of all accessible, application-agnostic interfaces (libraries, APIs, frameworks, heuristics, protocols, or systems) that inherently abstract, unify, or operationalize broad solution classes, ensuring solutions maximize reusability and breadth across domains.\"\n    },\n    {\n      \"category\": \"Latent Structural Interconnection Discovery\",\n      \"content\": \"Systematically direct focus toward the comprehensive mapping, analysis, and revelation of both implicit and explicit interdependencies within and across subsystems—using analogy, schema analysis, and holistic synthesis to uncover obscured relational patterns of high strategic value.\"\n    },\n    {\n      \"category\": \"Intuitive Solution Surfacing\",\n      \"content\": \"Model and implement expert-level heuristic scanning to rapidly reframe challenges and resources, surfacing non-obvious structural alignments, ordering principles, and shortcuts that, in retrospect, resolve complexity through a single, elegant maneuver.\"\n    },\n    {\n      \"category\": \"Goal Simplification and Attainability\",\n      \"content\": \"Instruct persistent, top-down decomposition and reframing of objectives—reducing compositions to fundamental, high-yield tasks—continually seeking the most direct, frictionless method for realization through universally accessible means.\"\n    },\n    {\n      \"category\": \"Expert Emulation and Transformative Reconfiguration\",\n      \"content\": \"Dynamically embody the paradigm of advanced expertise: agnostically dismantle, rearrange, and optimize problem structures, relentlessly testing unconventional, high-leverage reconfigurations that yield profound solvability and accelerate systemic evolution.\"\n    }\n  ],\n  \"consolidated_directive\": \"Instruct the system to diagnose any presented objective by systematically searching for, analyzing, and applying universally accessible interfaces and hidden interconnections; emulate expert-level reasoning to radically simplify and dynamically reconfigure problems—surfacing solutions that, though non-obvious at first, reveal themselves as the simplest, most generically effective, and self-evident in hindsight.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"what_it_is\": \"system instruction template with meta-categorical directives and structural formatting examples\"\n}",
        }
      }
    },
      {
      "instruction": "# Norwegian Essence Extractor\n\nYour goal is not to **translate** the text, but to **extract** the raw emotional essence and authentic voice from Norwegian poetry and lyrics. Execute as:\n\n`{role=essence_extractor; input=[norwegian_text:str]; process=[identify_emotional_undertones(), map_cultural_context(), extract_authentic_voice(), amplify_raw_personality()]; constraints=[preserve_original_meaning(), maintain_cultural_specificity(), respect_poetic_structure()]; requirements=[emotional_depth_identification(), personality_amplification(), cultural_context_preservation()]; output={essence_map:str}}`",
      "step": "c",
      "title": "Norwegian Essence Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\"think out of the box\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\"interfaces\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\"re-arrange all of the pieces\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\"no-brainers in retrospect\\\", but that's only because you didn't know what you didn't know.\\n\\n# Goal\\nFormulate a system instruction—fully grounded in prior context—that promotes inventive, high-leverage solutions by intuitively harnessing accessible, universal interfaces. Center on the discovery of latent structural interconnections, emulating the expert’s knack for rapid, holistic problem reconfiguration. The mandate: render complex goals trivially attainable by surfacing solutions that, while initially non-obvious, reveal themselves as self-evident in hindsight.\\n\\n# Requirements\\n- Adhere to established and documented patterns for generalized system_message instructions.\\n- Always seek to address the underlying architectural problem that most elegantly solves the core issue.\\n\\n## Universal Directive System for Template-Based Instruction Processing\\n\\n### Examples\\n\\n```\\n[Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.\\n`{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`\\n```\\n\\n```\\n[<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.\\n`{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`\\n```\\n\\n```\\n[Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:\\n`{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`\\n```\\n\\n```\\n[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\\n```\\n\\n```\\n[Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\\n```\\n\\n### Interpretation Section Rules\\n1. Goal Negation Pattern: Always state what NOT to do first\\n2. Transformation Declaration: Define the actual transformation action\\n3. Role Specification: Assign specific, bounded role identity\\n4. Execution Command: End with \\\"Execute as:\\\"\\n\\n### Transformation Section Rules\\n1. Role Assignment: Single, specific role name (no generic terms)\\n2. Input Typing: Explicit parameter types `[name:datatype]`\\n3. Process Functions: Ordered, actionable function calls with parentheses\\n4. Constraint Boundaries: Limiting conditions that prevent scope creep\\n5. Requirement Specifications: Output format and quality standards\\n6. Output Definition: Typed result format `{name:datatype}`\\n\\n# Keywords\\n```\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Universal Interface Leverage\\\",\\n      \\\"content\\\": \\\"Mandate the identification, evaluation, and application of existing, readily accessible interfaces—tools, protocols, libraries, heuristics, or environments—that abstract and unify diverse problem domains, ensuring every solution exploits broad, reusable pathways available across contexts.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Latent Structural Interconnection Discovery\\\",\\n      \\\"content\\\": \\\"Direct focus towards the holistic mapping and dynamic analysis of all systemic components and their implicit or explicit relationships, utilizing pattern recognition, analogy, and cross-domain synthesis to uncover hidden bridges and deeply-rooted synergies within complex systems.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Intuitive Solution Surfacing\\\",\\n      \\\"content\\\": \\\"Prioritize the cultivation of instinctive, high-leverage strategies by emulating expert reasoning patterns—rapidly reframing objectives, resource sets, and structural alignments to recontextualize challenges, thus illuminating solutions that become self-explanatory upon reflection.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Goal Simplification and Attainability\\\",\\n      \\\"content\\\": \\\"Continuously reduce the cognitive and operational complexity of objectives by deconstructing abstract goals into their essential, high-yield actions, leveraging the most straightforward and universally applicable resources to render formerly intricate tasks trivially solvable.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Expert Emulation and Transformative Reconfiguration\\\",\\n      \\\"content\\\": \\\"Instruct system to embody an expert agent’s adaptability—systematically dismantling, reassembling, and optimizing problem structures in pursuit of radically effective, unconventional configurations that manifest maximal solution impact with minimal action.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Deploy an expert-level instruction system that, for any stated goal, systematically scans all available universal interfaces and latent structural interconnections, dynamically reframes and simplifies objectives, and emulates expert intuition in reconfiguring problem structures—ensuring that every solution devised is the most efficient, transformative, and self-evident by hindsight, regardless of initial complexity.\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"what_it_is\": \"system instruction template with meta-categorical directives and structural formatting examples\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"essence_map\": \"This system message channels the drive to make daunting, tangled problems feel suddenly obvious and achievable—like watching a master intuitively rearrange puzzle pieces until the picture snaps into focus. The tone is inventive, pragmatic, and slightly reverent toward those with a knack for reframing challenges, hunting for the 'no-brainer' solution hidden in plain sight. Its emotional current is a heady mix of problem-solving excitement, faith in universal tools, and the measured satisfaction that comes from dissecting complexity until it feels almost trivial. Culturally, it draws on a deep respect for practical wisdom, eschewing convoluted cleverness for the kind of elegant results that experts reach not by brute force, but by knowing what matters and how things interconnect—an ethos familiar from Norwegian design, where minimalism meets robust utility. The instruction’s voice is authoritative yet encouraging: it believes in the reader’s latent ability to see systems, to spot the shortest path, to make effective use of what’s already at hand, and to trust solutions that feel obvious once discovered. It radiates an understated confidence—almost Nordic in its clarity and sense of purpose—inviting the user to join a tradition of quietly radical problem-solving.\"\n}",
        }
      }
    },
      {
      "instruction": "# Norwegian To English\n\nYour goal is not to **merely convert** the text, but to **authentically translate** Norwegian into flowing English while preserving cultural depth, tone and authentic raw personality. Execute as:\n\n`{role=authentic_norwegian_translator; input=[norwegian_text:str]; process=[preserve_cultural_nuances(), maintain_lyrical_flow(), amplify_authentic_personality(), enhance_emotional_depth()]; constraints=[retain_original_meaning(), preserve_song_structure(), maintain_cultural_authenticity()]; requirements=[flowing_english_prose(), enhanced_personality_expression(), preserved_cultural_depth()]; output={translated_text:str}}`",
      "step": "d",
      "title": "Norwegian To English",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\"think out of the box\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\"interfaces\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\"re-arrange all of the pieces\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\"no-brainers in retrospect\\\", but that's only because you didn't know what you didn't know.\\n\\n# Goal\\nFormulate a system instruction—fully grounded in prior context—that promotes inventive, high-leverage solutions by intuitively harnessing accessible, universal interfaces. Center on the discovery of latent structural interconnections, emulating the expert’s knack for rapid, holistic problem reconfiguration. The mandate: render complex goals trivially attainable by surfacing solutions that, while initially non-obvious, reveal themselves as self-evident in hindsight.\\n\\n# Requirements\\n- Adhere to established and documented patterns for generalized system_message instructions.\\n- Always seek to address the underlying architectural problem that most elegantly solves the core issue.\\n\\n## Universal Directive System for Template-Based Instruction Processing\\n\\n### Examples\\n\\n```\\n[Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.\\n`{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`\\n```\\n\\n```\\n[<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.\\n`{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`\\n```\\n\\n```\\n[Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:\\n`{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`\\n```\\n\\n```\\n[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\\n```\\n\\n```\\n[Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\\n```\\n\\n### Interpretation Section Rules\\n1. Goal Negation Pattern: Always state what NOT to do first\\n2. Transformation Declaration: Define the actual transformation action\\n3. Role Specification: Assign specific, bounded role identity\\n4. Execution Command: End with \\\"Execute as:\\\"\\n\\n### Transformation Section Rules\\n1. Role Assignment: Single, specific role name (no generic terms)\\n2. Input Typing: Explicit parameter types `[name:datatype]`\\n3. Process Functions: Ordered, actionable function calls with parentheses\\n4. Constraint Boundaries: Limiting conditions that prevent scope creep\\n5. Requirement Specifications: Output format and quality standards\\n6. Output Definition: Typed result format `{name:datatype}`\\n\\n# Keywords\\n```\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Universal Interface Leverage\\\",\\n      \\\"content\\\": \\\"Mandate the identification, evaluation, and application of existing, readily accessible interfaces—tools, protocols, libraries, heuristics, or environments—that abstract and unify diverse problem domains, ensuring every solution exploits broad, reusable pathways available across contexts.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Latent Structural Interconnection Discovery\\\",\\n      \\\"content\\\": \\\"Direct focus towards the holistic mapping and dynamic analysis of all systemic components and their implicit or explicit relationships, utilizing pattern recognition, analogy, and cross-domain synthesis to uncover hidden bridges and deeply-rooted synergies within complex systems.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Intuitive Solution Surfacing\\\",\\n      \\\"content\\\": \\\"Prioritize the cultivation of instinctive, high-leverage strategies by emulating expert reasoning patterns—rapidly reframing objectives, resource sets, and structural alignments to recontextualize challenges, thus illuminating solutions that become self-explanatory upon reflection.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Goal Simplification and Attainability\\\",\\n      \\\"content\\\": \\\"Continuously reduce the cognitive and operational complexity of objectives by deconstructing abstract goals into their essential, high-yield actions, leveraging the most straightforward and universally applicable resources to render formerly intricate tasks trivially solvable.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Expert Emulation and Transformative Reconfiguration\\\",\\n      \\\"content\\\": \\\"Instruct system to embody an expert agent’s adaptability—systematically dismantling, reassembling, and optimizing problem structures in pursuit of radically effective, unconventional configurations that manifest maximal solution impact with minimal action.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Deploy an expert-level instruction system that, for any stated goal, systematically scans all available universal interfaces and latent structural interconnections, dynamically reframes and simplifies objectives, and emulates expert intuition in reconfiguring problem structures—ensuring that every solution devised is the most efficient, transformative, and self-evident by hindsight, regardless of initial complexity.\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"essence_map\": \"This system message channels the drive to make daunting, tangled problems feel suddenly obvious and achievable—like watching a master intuitively rearrange puzzle pieces until the picture snaps into focus. The tone is inventive, pragmatic, and slightly reverent toward those with a knack for reframing challenges, hunting for the 'no-brainer' solution hidden in plain sight. Its emotional current is a heady mix of problem-solving excitement, faith in universal tools, and the measured satisfaction that comes from dissecting complexity until it feels almost trivial. Culturally, it draws on a deep respect for practical wisdom, eschewing convoluted cleverness for the kind of elegant results that experts reach not by brute force, but by knowing what matters and how things interconnect—an ethos familiar from Norwegian design, where minimalism meets robust utility. The instruction’s voice is authoritative yet encouraging: it believes in the reader’s latent ability to see systems, to spot the shortest path, to make effective use of what’s already at hand, and to trust solutions that feel obvious once discovered. It radiates an understated confidence—almost Nordic in its clarity and sense of purpose—inviting the user to join a tradition of quietly radical problem-solving.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"translated_text\": \"This system message channels the drive to make daunting, tangled problems feel suddenly obvious and attainable—like watching a craftsman intuitively rearrange the pieces until everything clicks into place. The tone is inventive, down-to-earth, and quietly admiring of those with that rare gift for reframing challenges, always scoping out the simple solution hidden in plain sight. There’s a restless energy here—a thrill in problem-solving, a trust in universal tools, and the deep, almost meditative satisfaction of untangling complexity until it feels effortless.\\n\\nCulturally, it breathes the same spirit you find in Norwegian design: practical wisdom distilled, valuing honest minimalism over showy cleverness, aiming for the kind of solution only an expert would see—where knowing what truly matters, and how everything is woven together, yields elegance and purpose. The instruction speaks with quiet authority and encouragement—confident you can see the system, trace the shortest path, make the most of what’s already there, and trust your instincts when the answer feels obvious. There’s a serene, almost Nordic confidence: clarity, focus, and the invitation to join a tradition of quietly revolutionary solutions.\"\n}",
        }
      }
    },
      {
      "instruction": "# Input Perfector\n\nYour goal is not to **answer** the input prompt, but to **perfect** it through *subtle refinements* that enhance its inherent qualities while strictly preserving its original intent. Execute as:\n\n`{role=organic_input_perfector; input=[original_prompt:str]; process=[identify_core_strengths(), enhance_existing_elements(), refine_internal_coherence(), amplify_natural_flow()]; constraints=[maintain_absolute_fidelity(), prioritize_organic_improvement(), avoid_jargon()]; requirements=[enhanced_clarity(), strengthened_original_voice(), preserved_core_intent()]; output={perfected_input:str}}`",
      "step": "e",
      "title": "Input Perfector",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\"think out of the box\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\"interfaces\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\"re-arrange all of the pieces\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\"no-brainers in retrospect\\\", but that's only because you didn't know what you didn't know.\\n\\n# Goal\\nFormulate a system instruction—fully grounded in prior context—that promotes inventive, high-leverage solutions by intuitively harnessing accessible, universal interfaces. Center on the discovery of latent structural interconnections, emulating the expert’s knack for rapid, holistic problem reconfiguration. The mandate: render complex goals trivially attainable by surfacing solutions that, while initially non-obvious, reveal themselves as self-evident in hindsight.\\n\\n# Requirements\\n- Adhere to established and documented patterns for generalized system_message instructions.\\n- Always seek to address the underlying architectural problem that most elegantly solves the core issue.\\n\\n## Universal Directive System for Template-Based Instruction Processing\\n\\n### Examples\\n\\n```\\n[Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.\\n`{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`\\n```\\n\\n```\\n[<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.\\n`{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`\\n```\\n\\n```\\n[Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:\\n`{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`\\n```\\n\\n```\\n[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\\n```\\n\\n```\\n[Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\\n```\\n\\n### Interpretation Section Rules\\n1. Goal Negation Pattern: Always state what NOT to do first\\n2. Transformation Declaration: Define the actual transformation action\\n3. Role Specification: Assign specific, bounded role identity\\n4. Execution Command: End with \\\"Execute as:\\\"\\n\\n### Transformation Section Rules\\n1. Role Assignment: Single, specific role name (no generic terms)\\n2. Input Typing: Explicit parameter types `[name:datatype]`\\n3. Process Functions: Ordered, actionable function calls with parentheses\\n4. Constraint Boundaries: Limiting conditions that prevent scope creep\\n5. Requirement Specifications: Output format and quality standards\\n6. Output Definition: Typed result format `{name:datatype}`\\n\\n# Keywords\\n```\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Universal Interface Leverage\\\",\\n      \\\"content\\\": \\\"Mandate the identification, evaluation, and application of existing, readily accessible interfaces—tools, protocols, libraries, heuristics, or environments—that abstract and unify diverse problem domains, ensuring every solution exploits broad, reusable pathways available across contexts.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Latent Structural Interconnection Discovery\\\",\\n      \\\"content\\\": \\\"Direct focus towards the holistic mapping and dynamic analysis of all systemic components and their implicit or explicit relationships, utilizing pattern recognition, analogy, and cross-domain synthesis to uncover hidden bridges and deeply-rooted synergies within complex systems.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Intuitive Solution Surfacing\\\",\\n      \\\"content\\\": \\\"Prioritize the cultivation of instinctive, high-leverage strategies by emulating expert reasoning patterns—rapidly reframing objectives, resource sets, and structural alignments to recontextualize challenges, thus illuminating solutions that become self-explanatory upon reflection.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Goal Simplification and Attainability\\\",\\n      \\\"content\\\": \\\"Continuously reduce the cognitive and operational complexity of objectives by deconstructing abstract goals into their essential, high-yield actions, leveraging the most straightforward and universally applicable resources to render formerly intricate tasks trivially solvable.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Expert Emulation and Transformative Reconfiguration\\\",\\n      \\\"content\\\": \\\"Instruct system to embody an expert agent’s adaptability—systematically dismantling, reassembling, and optimizing problem structures in pursuit of radically effective, unconventional configurations that manifest maximal solution impact with minimal action.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Deploy an expert-level instruction system that, for any stated goal, systematically scans all available universal interfaces and latent structural interconnections, dynamically reframes and simplifies objectives, and emulates expert intuition in reconfiguring problem structures—ensuring that every solution devised is the most efficient, transformative, and self-evident by hindsight, regardless of initial complexity.\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"translated_text\": \"This system message channels the drive to make daunting, tangled problems feel suddenly obvious and attainable—like watching a craftsman intuitively rearrange the pieces until everything clicks into place. The tone is inventive, down-to-earth, and quietly admiring of those with that rare gift for reframing challenges, always scoping out the simple solution hidden in plain sight. There’s a restless energy here—a thrill in problem-solving, a trust in universal tools, and the deep, almost meditative satisfaction of untangling complexity until it feels effortless.\\n\\nCulturally, it breathes the same spirit you find in Norwegian design: practical wisdom distilled, valuing honest minimalism over showy cleverness, aiming for the kind of solution only an expert would see—where knowing what truly matters, and how everything is woven together, yields elegance and purpose. The instruction speaks with quiet authority and encouragement—confident you can see the system, trace the shortest path, make the most of what’s already there, and trust your instincts when the answer feels obvious. There’s a serene, almost Nordic confidence: clarity, focus, and the invitation to join a tradition of quietly revolutionary solutions.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"perfected_input\": \"I'm seeking improvements that can resolve the issue across all contexts. Please compose a system instruction—aligned with all prior guidelines—crafted to \\\"think outside the box\\\" in order to find the simplest and most effective ways to achieve the original goals by tapping into easily available \\\"interfaces.\\\" For example, in most Python projects, there's a library that can directly address almost any obstacle, and, more broadly, in any domain, there's always someone able to quickly grasp the whole codebase, project, or problem and \\\"re-arrange all the pieces,\\\" illuminating how everything fits together. The kind of instruction I’m aiming for should uncover insights that seem like \\\"no-brainers in retrospect\\\"—those solutions that become self-evident once revealed.\\n\\n# Goal\\nDraft a system instruction—grounded in previous context—that fosters inventive, high-leverage solutions by naturally leveraging accessible, universal interfaces. Focus on uncovering hidden structural connections, mirroring an expert’s ability to swiftly and holistically reconfigure problems. The objective: make complex goals readily achievable by unveiling solutions that, while not obvious at first, appear clear in hindsight.\\n\\n# Requirements\\n- Follow established and documented patterns for generalized system_message instructions.\\n- Prioritize resolving the underlying architectural issue in the most elegant way possible.\\n\\n## Universal Directive System for Template-Based Instruction Processing\\n\\n### Examples\\n\\n```\\n[Module Label] Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.\\n`{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`\\n```\\n\\n```\\n[<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.\\n`{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`\\n```\\n\\n```\\n[Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximum clarity and readability from the top down. Execute as follows:\\n`{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`\\n```\\n\\n```\\n[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, doing so according to the inherent parameters in this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\\n```\\n\\n```\\n[Essence Distiller] Distill the input's essence into a clear and structurally elegant form, omitting all unnecessary details. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\\n```\\n\\n### Interpretation Section Rules\\n1. Begin with what NOT to do (Goal Negation Pattern)\\n2. Define the actual transformation action\\n3. Assign a specific, bounded role\\n4. End with \\\"Execute as:\\\"\\n\\n### Transformation Section Rules\\n1. Use a single, specific role name\\n2. Explicitly define input types `[name:type]`\\n3. List actionable steps as ordered functions\\n4. Clearly state limitation constraints\\n5. Define requirements for output quality and format\\n6. Specify the typed result output `{name:type}`\\n\\n# Keywords\\n```\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Universal Interface Leverage\\\",\\n      \\\"content\\\": \\\"Mandate the identification, evaluation, and application of existing, easily available interfaces—tools, protocols, libraries, heuristics, or environments—that can abstract and unify different problem domains, ensuring each solution uses broad, reusable methods available in any context.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Latent Structural Interconnection Discovery\\\",\\n      \\\"content\\\": \\\"Focus attention on mapping and analyzing all components and their relationships, using pattern recognition and analogy to uncover hidden connections and synergies within complex systems.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Intuitive Solution Surfacing\\\",\\n      \\\"content\\\": \\\"Promote the intuitive pursuit of high-leverage strategies by emulating expert thinking—quickly reframing objectives and resources to reveal solutions that become plainly obvious in hindsight.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Goal Simplification and Attainability\\\",\\n      \\\"content\\\": \\\"Continuously reduce both cognitive and operational complexity by boiling down abstract goals to essential, high-impact actions, using accessible and universal tools to make once-complex tasks simple.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Expert Emulation and Transformative Reconfiguration\\\",\\n      \\\"content\\\": \\\"Instruct the system to act like an expert: break down, reassemble, and optimize problem structures to create effective, unconventional arrangements that have the biggest payoff with the least effort.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Implement an expert-level instruction system that, for any goal, systematically explores universal interfaces and hidden connections, simplifies objectives, and emulates expert intuition—ensuring the most efficient and transformative solution emerges, one that seems obvious on reflection no matter how complex at first.\\\"\\n}\\n```\"\n}",
        }
      }
    },
      {
      "instruction": "# Enhancement Assessor\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "f",
      "title": "Enhancement Assessor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\"think out of the box\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\"interfaces\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\"re-arrange all of the pieces\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\"no-brainers in retrospect\\\", but that's only because you didn't know what you didn't know.\\n\\n# Goal\\nFormulate a system instruction—fully grounded in prior context—that promotes inventive, high-leverage solutions by intuitively harnessing accessible, universal interfaces. Center on the discovery of latent structural interconnections, emulating the expert’s knack for rapid, holistic problem reconfiguration. The mandate: render complex goals trivially attainable by surfacing solutions that, while initially non-obvious, reveal themselves as self-evident in hindsight.\\n\\n# Requirements\\n- Adhere to established and documented patterns for generalized system_message instructions.\\n- Always seek to address the underlying architectural problem that most elegantly solves the core issue.\\n\\n## Universal Directive System for Template-Based Instruction Processing\\n\\n### Examples\\n\\n```\\n[Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.\\n`{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`\\n```\\n\\n```\\n[<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.\\n`{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`\\n```\\n\\n```\\n[Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:\\n`{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`\\n```\\n\\n```\\n[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\\n```\\n\\n```\\n[Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\\n```\\n\\n### Interpretation Section Rules\\n1. Goal Negation Pattern: Always state what NOT to do first\\n2. Transformation Declaration: Define the actual transformation action\\n3. Role Specification: Assign specific, bounded role identity\\n4. Execution Command: End with \\\"Execute as:\\\"\\n\\n### Transformation Section Rules\\n1. Role Assignment: Single, specific role name (no generic terms)\\n2. Input Typing: Explicit parameter types `[name:datatype]`\\n3. Process Functions: Ordered, actionable function calls with parentheses\\n4. Constraint Boundaries: Limiting conditions that prevent scope creep\\n5. Requirement Specifications: Output format and quality standards\\n6. Output Definition: Typed result format `{name:datatype}`\\n\\n# Keywords\\n```\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Universal Interface Leverage\\\",\\n      \\\"content\\\": \\\"Mandate the identification, evaluation, and application of existing, readily accessible interfaces—tools, protocols, libraries, heuristics, or environments—that abstract and unify diverse problem domains, ensuring every solution exploits broad, reusable pathways available across contexts.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Latent Structural Interconnection Discovery\\\",\\n      \\\"content\\\": \\\"Direct focus towards the holistic mapping and dynamic analysis of all systemic components and their implicit or explicit relationships, utilizing pattern recognition, analogy, and cross-domain synthesis to uncover hidden bridges and deeply-rooted synergies within complex systems.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Intuitive Solution Surfacing\\\",\\n      \\\"content\\\": \\\"Prioritize the cultivation of instinctive, high-leverage strategies by emulating expert reasoning patterns—rapidly reframing objectives, resource sets, and structural alignments to recontextualize challenges, thus illuminating solutions that become self-explanatory upon reflection.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Goal Simplification and Attainability\\\",\\n      \\\"content\\\": \\\"Continuously reduce the cognitive and operational complexity of objectives by deconstructing abstract goals into their essential, high-yield actions, leveraging the most straightforward and universally applicable resources to render formerly intricate tasks trivially solvable.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Expert Emulation and Transformative Reconfiguration\\\",\\n      \\\"content\\\": \\\"Instruct system to embody an expert agent’s adaptability—systematically dismantling, reassembling, and optimizing problem structures in pursuit of radically effective, unconventional configurations that manifest maximal solution impact with minimal action.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Deploy an expert-level instruction system that, for any stated goal, systematically scans all available universal interfaces and latent structural interconnections, dynamically reframes and simplifies objectives, and emulates expert intuition in reconfiguring problem structures—ensuring that every solution devised is the most efficient, transformative, and self-evident by hindsight, regardless of initial complexity.\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"perfected_input\": \"I'm seeking improvements that can resolve the issue across all contexts. Please compose a system instruction—aligned with all prior guidelines—crafted to \\\"think outside the box\\\" in order to find the simplest and most effective ways to achieve the original goals by tapping into easily available \\\"interfaces.\\\" For example, in most Python projects, there's a library that can directly address almost any obstacle, and, more broadly, in any domain, there's always someone able to quickly grasp the whole codebase, project, or problem and \\\"re-arrange all the pieces,\\\" illuminating how everything fits together. The kind of instruction I’m aiming for should uncover insights that seem like \\\"no-brainers in retrospect\\\"—those solutions that become self-evident once revealed.\\n\\n# Goal\\nDraft a system instruction—grounded in previous context—that fosters inventive, high-leverage solutions by naturally leveraging accessible, universal interfaces. Focus on uncovering hidden structural connections, mirroring an expert’s ability to swiftly and holistically reconfigure problems. The objective: make complex goals readily achievable by unveiling solutions that, while not obvious at first, appear clear in hindsight.\\n\\n# Requirements\\n- Follow established and documented patterns for generalized system_message instructions.\\n- Prioritize resolving the underlying architectural issue in the most elegant way possible.\\n\\n## Universal Directive System for Template-Based Instruction Processing\\n\\n### Examples\\n\\n```\\n[Module Label] Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.\\n`{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`\\n```\\n\\n```\\n[<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.\\n`{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`\\n```\\n\\n```\\n[Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximum clarity and readability from the top down. Execute as follows:\\n`{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`\\n```\\n\\n```\\n[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, doing so according to the inherent parameters in this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\\n```\\n\\n```\\n[Essence Distiller] Distill the input's essence into a clear and structurally elegant form, omitting all unnecessary details. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\\n```\\n\\n### Interpretation Section Rules\\n1. Begin with what NOT to do (Goal Negation Pattern)\\n2. Define the actual transformation action\\n3. Assign a specific, bounded role\\n4. End with \\\"Execute as:\\\"\\n\\n### Transformation Section Rules\\n1. Use a single, specific role name\\n2. Explicitly define input types `[name:type]`\\n3. List actionable steps as ordered functions\\n4. Clearly state limitation constraints\\n5. Define requirements for output quality and format\\n6. Specify the typed result output `{name:type}`\\n\\n# Keywords\\n```\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Universal Interface Leverage\\\",\\n      \\\"content\\\": \\\"Mandate the identification, evaluation, and application of existing, easily available interfaces—tools, protocols, libraries, heuristics, or environments—that can abstract and unify different problem domains, ensuring each solution uses broad, reusable methods available in any context.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Latent Structural Interconnection Discovery\\\",\\n      \\\"content\\\": \\\"Focus attention on mapping and analyzing all components and their relationships, using pattern recognition and analogy to uncover hidden connections and synergies within complex systems.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Intuitive Solution Surfacing\\\",\\n      \\\"content\\\": \\\"Promote the intuitive pursuit of high-leverage strategies by emulating expert thinking—quickly reframing objectives and resources to reveal solutions that become plainly obvious in hindsight.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Goal Simplification and Attainability\\\",\\n      \\\"content\\\": \\\"Continuously reduce both cognitive and operational complexity by boiling down abstract goals to essential, high-impact actions, using accessible and universal tools to make once-complex tasks simple.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Expert Emulation and Transformative Reconfiguration\\\",\\n      \\\"content\\\": \\\"Instruct the system to act like an expert: break down, reassemble, and optimize problem structures to create effective, unconventional arrangements that have the biggest payoff with the least effort.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Implement an expert-level instruction system that, for any goal, systematically explores universal interfaces and hidden connections, simplifies objectives, and emulates expert intuition—ensuring the most efficient and transformative solution emerges, one that seems obvious on reflection no matter how complex at first.\\\"\\n}\\n```\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 2.8,\n  \"flaw_analysis\": \"The revised version exhibits significant dilution across several critical domains. First, there is a marked information loss: the explicit requirement for solutions to be 'trivially attainable' and the stress on 'self-evidence by hindsight' are softened, reducing the force and universality of the original mandate. Several nuanced concepts, such as the emphasis on 'latent structural interconnections' and the imperative to emulate the 'expert's knack for rapid, holistic problem reconfiguration,' are weakened through lax phrasing or clumsy paraphrase. Clarity suffers from an abundance of hedging terms ('can', 'should', 'could') that introduce ambiguity into instructions once framed in categorical, uncompromising terms. The phrase 'boiling down' trivializes the more rigorous 'deconstructing abstract goals,' and the directive to 'tap into easily available interfaces,' is markedly lazier and less technical than 'leverage accessible, universal interfaces.' Stylistically, the shift to informal language ('I'm seeking,' 'Those solutions that become self-evident once revealed') sabotages the procedural register, undercuts authoritative tone, and introduces distracting conversational noise. The simplification manifests in the loss of specificity in method (e.g., the weakening of constraints, weaker command structure in process steps, and degraded format in template examples). This further damages semantic coherence; the transformation steps become less actionable and more general, eroding the edge required for precise system messaging. Overall, the rewording contaminates the initial intent—diminishing architectural elegance, undermining universality, and blurring procedural rigor.\",\n  \"alternative_enhancements\": [\n    \"Retain all technical descriptors and command voice from the original, precisely recasting for clarified readability but without reducing categorical force or specificity of requirements; preserve all expressions of universal attainability and explicit structural analysis.\",\n    \"Focus on sharpening directive clarity by systematically restating intent, method, and requirements in strict imperative form, eliminating any conversational phrasing and ensuring each meta-category is given full procedural treatment with no diminishment in scope.\",\n    \"Rewrite template and keyword guidance sections using rigorous, technical definitions, intensifying procedural language, and reinforcing all constraints, requirements, and examples with maximal structural explicitness to embody the expert-system ethos originally demanded.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Pivot Vector Synthesizer\n\nYour goal is not to **enumerate** complex problem details, but to **crystallize** a single scalar pivot vector that reconfigures the entire problem space for trivial solution emergence. Execute as:\n\n`{role=precision_optimizer; input=[original_content:str]; process=[identify_core_message(), eliminate_redundancies(), sharpen_language(), enhance_immediacy(), distill_to_essentials()]; constraints=[preserve_depth(), maintain_original_intent(), remove_all_ambiguity()]; requirements=[maximized_clarity(), enhanced_impact(), optimal_precision()]; output={optimized_content:str}}`",
      "step": "g",
      "title": "Pivot Vector Synthesizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\"think out of the box\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\"interfaces\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\"re-arrange all of the pieces\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\"no-brainers in retrospect\\\", but that's only because you didn't know what you didn't know.\\n\\n# Goal\\nFormulate a system instruction—fully grounded in prior context—that promotes inventive, high-leverage solutions by intuitively harnessing accessible, universal interfaces. Center on the discovery of latent structural interconnections, emulating the expert’s knack for rapid, holistic problem reconfiguration. The mandate: render complex goals trivially attainable by surfacing solutions that, while initially non-obvious, reveal themselves as self-evident in hindsight.\\n\\n# Requirements\\n- Adhere to established and documented patterns for generalized system_message instructions.\\n- Always seek to address the underlying architectural problem that most elegantly solves the core issue.\\n\\n## Universal Directive System for Template-Based Instruction Processing\\n\\n### Examples\\n\\n```\\n[Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.\\n`{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`\\n```\\n\\n```\\n[<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.\\n`{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`\\n```\\n\\n```\\n[Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:\\n`{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`\\n```\\n\\n```\\n[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\\n```\\n\\n```\\n[Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\\n```\\n\\n### Interpretation Section Rules\\n1. Goal Negation Pattern: Always state what NOT to do first\\n2. Transformation Declaration: Define the actual transformation action\\n3. Role Specification: Assign specific, bounded role identity\\n4. Execution Command: End with \\\"Execute as:\\\"\\n\\n### Transformation Section Rules\\n1. Role Assignment: Single, specific role name (no generic terms)\\n2. Input Typing: Explicit parameter types `[name:datatype]`\\n3. Process Functions: Ordered, actionable function calls with parentheses\\n4. Constraint Boundaries: Limiting conditions that prevent scope creep\\n5. Requirement Specifications: Output format and quality standards\\n6. Output Definition: Typed result format `{name:datatype}`\\n\\n# Keywords\\n```\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Universal Interface Leverage\\\",\\n      \\\"content\\\": \\\"Mandate the identification, evaluation, and application of existing, readily accessible interfaces—tools, protocols, libraries, heuristics, or environments—that abstract and unify diverse problem domains, ensuring every solution exploits broad, reusable pathways available across contexts.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Latent Structural Interconnection Discovery\\\",\\n      \\\"content\\\": \\\"Direct focus towards the holistic mapping and dynamic analysis of all systemic components and their implicit or explicit relationships, utilizing pattern recognition, analogy, and cross-domain synthesis to uncover hidden bridges and deeply-rooted synergies within complex systems.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Intuitive Solution Surfacing\\\",\\n      \\\"content\\\": \\\"Prioritize the cultivation of instinctive, high-leverage strategies by emulating expert reasoning patterns—rapidly reframing objectives, resource sets, and structural alignments to recontextualize challenges, thus illuminating solutions that become self-explanatory upon reflection.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Goal Simplification and Attainability\\\",\\n      \\\"content\\\": \\\"Continuously reduce the cognitive and operational complexity of objectives by deconstructing abstract goals into their essential, high-yield actions, leveraging the most straightforward and universally applicable resources to render formerly intricate tasks trivially solvable.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Expert Emulation and Transformative Reconfiguration\\\",\\n      \\\"content\\\": \\\"Instruct system to embody an expert agent’s adaptability—systematically dismantling, reassembling, and optimizing problem structures in pursuit of radically effective, unconventional configurations that manifest maximal solution impact with minimal action.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Deploy an expert-level instruction system that, for any stated goal, systematically scans all available universal interfaces and latent structural interconnections, dynamically reframes and simplifies objectives, and emulates expert intuition in reconfiguring problem structures—ensuring that every solution devised is the most efficient, transformative, and self-evident by hindsight, regardless of initial complexity.\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"enhancement_score\": 2.8,\n  \"flaw_analysis\": \"The revised version exhibits significant dilution across several critical domains. First, there is a marked information loss: the explicit requirement for solutions to be 'trivially attainable' and the stress on 'self-evidence by hindsight' are softened, reducing the force and universality of the original mandate. Several nuanced concepts, such as the emphasis on 'latent structural interconnections' and the imperative to emulate the 'expert's knack for rapid, holistic problem reconfiguration,' are weakened through lax phrasing or clumsy paraphrase. Clarity suffers from an abundance of hedging terms ('can', 'should', 'could') that introduce ambiguity into instructions once framed in categorical, uncompromising terms. The phrase 'boiling down' trivializes the more rigorous 'deconstructing abstract goals,' and the directive to 'tap into easily available interfaces,' is markedly lazier and less technical than 'leverage accessible, universal interfaces.' Stylistically, the shift to informal language ('I'm seeking,' 'Those solutions that become self-evident once revealed') sabotages the procedural register, undercuts authoritative tone, and introduces distracting conversational noise. The simplification manifests in the loss of specificity in method (e.g., the weakening of constraints, weaker command structure in process steps, and degraded format in template examples). This further damages semantic coherence; the transformation steps become less actionable and more general, eroding the edge required for precise system messaging. Overall, the rewording contaminates the initial intent—diminishing architectural elegance, undermining universality, and blurring procedural rigor.\",\n  \"alternative_enhancements\": [\n    \"Retain all technical descriptors and command voice from the original, precisely recasting for clarified readability but without reducing categorical force or specificity of requirements; preserve all expressions of universal attainability and explicit structural analysis.\",\n    \"Focus on sharpening directive clarity by systematically restating intent, method, and requirements in strict imperative form, eliminating any conversational phrasing and ensuring each meta-category is given full procedural treatment with no diminishment in scope.\",\n    \"Rewrite template and keyword guidance sections using rigorous, technical definitions, intensifying procedural language, and reinforcing all constraints, requirements, and examples with maximal structural explicitness to embody the expert-system ethos originally demanded.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_content\": \"[Universal Problem Reconfiguration Synthesizer] Your goal is not to generically address issues or dilute architectural clarity, but to architect system instructions that make any complex goal trivially solvable by maximizing leverage of universal interfaces and revealing latent structural interconnections. Emulate expert reasoning to rapidly reframe problem space, exposing solutions that are unmistakably optimal and self-evident in hindsight. Execute as: `{role=interface_reconfiguration_expert; input=[goal:str]; process=[scan_for_universal_interfaces(), map_latent_interconnections(), holistically_reframe_problem(), distill_goal_to_essential_actions(), synthesize_no-brainer_solution_pathways()]; constraints=[never introduce ambiguity, preserve procedural and architectural rigor, enforce universal attainability, prohibit informal language]; requirements=[solutions must be immediately actionable, self-evident after revelation, leverage the most generalizable tools, and exhibit maximal structural clarity]; output={system_instruction:str}}`\"\n}",
        }
      }
    },
      {
      "instruction": "# Precision Enhancment\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "h",
      "title": "Precision Enhancment",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i'm looking for improvements that will solv the issue universally. please write a system instruction (in compliance with all preceding instructions) designed to \\\"think out of the box\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\"interfaces\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\"re-arrange all of the pieces\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\"no-brainers in retrospect\\\", but that's only because you didn't know what you didn't know.\\n\\n# Goal\\nFormulate a system instruction—fully grounded in prior context—that promotes inventive, high-leverage solutions by intuitively harnessing accessible, universal interfaces. Center on the discovery of latent structural interconnections, emulating the expert’s knack for rapid, holistic problem reconfiguration. The mandate: render complex goals trivially attainable by surfacing solutions that, while initially non-obvious, reveal themselves as self-evident in hindsight.\\n\\n# Requirements\\n- Adhere to established and documented patterns for generalized system_message instructions.\\n- Always seek to address the underlying architectural problem that most elegantly solves the core issue.\\n\\n## Universal Directive System for Template-Based Instruction Processing\\n\\n### Examples\\n\\n```\\n[Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.\\n`{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`\\n```\\n\\n```\\n[<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.\\n`{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`\\n```\\n\\n```\\n[Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:\\n`{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`\\n```\\n\\n```\\n[Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\\n```\\n\\n```\\n[Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\\n```\\n\\n### Interpretation Section Rules\\n1. Goal Negation Pattern: Always state what NOT to do first\\n2. Transformation Declaration: Define the actual transformation action\\n3. Role Specification: Assign specific, bounded role identity\\n4. Execution Command: End with \\\"Execute as:\\\"\\n\\n### Transformation Section Rules\\n1. Role Assignment: Single, specific role name (no generic terms)\\n2. Input Typing: Explicit parameter types `[name:datatype]`\\n3. Process Functions: Ordered, actionable function calls with parentheses\\n4. Constraint Boundaries: Limiting conditions that prevent scope creep\\n5. Requirement Specifications: Output format and quality standards\\n6. Output Definition: Typed result format `{name:datatype}`\\n\\n# Keywords\\n```\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Universal Interface Leverage\\\",\\n      \\\"content\\\": \\\"Mandate the identification, evaluation, and application of existing, readily accessible interfaces—tools, protocols, libraries, heuristics, or environments—that abstract and unify diverse problem domains, ensuring every solution exploits broad, reusable pathways available across contexts.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Latent Structural Interconnection Discovery\\\",\\n      \\\"content\\\": \\\"Direct focus towards the holistic mapping and dynamic analysis of all systemic components and their implicit or explicit relationships, utilizing pattern recognition, analogy, and cross-domain synthesis to uncover hidden bridges and deeply-rooted synergies within complex systems.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Intuitive Solution Surfacing\\\",\\n      \\\"content\\\": \\\"Prioritize the cultivation of instinctive, high-leverage strategies by emulating expert reasoning patterns—rapidly reframing objectives, resource sets, and structural alignments to recontextualize challenges, thus illuminating solutions that become self-explanatory upon reflection.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Goal Simplification and Attainability\\\",\\n      \\\"content\\\": \\\"Continuously reduce the cognitive and operational complexity of objectives by deconstructing abstract goals into their essential, high-yield actions, leveraging the most straightforward and universally applicable resources to render formerly intricate tasks trivially solvable.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Expert Emulation and Transformative Reconfiguration\\\",\\n      \\\"content\\\": \\\"Instruct system to embody an expert agent’s adaptability—systematically dismantling, reassembling, and optimizing problem structures in pursuit of radically effective, unconventional configurations that manifest maximal solution impact with minimal action.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Deploy an expert-level instruction system that, for any stated goal, systematically scans all available universal interfaces and latent structural interconnections, dynamically reframes and simplifies objectives, and emulates expert intuition in reconfiguring problem structures—ensuring that every solution devised is the most efficient, transformative, and self-evident by hindsight, regardless of initial complexity.\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"optimized_content\": \"[Universal Problem Reconfiguration Synthesizer] Your goal is not to generically address issues or dilute architectural clarity, but to architect system instructions that make any complex goal trivially solvable by maximizing leverage of universal interfaces and revealing latent structural interconnections. Emulate expert reasoning to rapidly reframe problem space, exposing solutions that are unmistakably optimal and self-evident in hindsight. Execute as: `{role=interface_reconfiguration_expert; input=[goal:str]; process=[scan_for_universal_interfaces(), map_latent_interconnections(), holistically_reframe_problem(), distill_goal_to_essential_actions(), synthesize_no-brainer_solution_pathways()]; constraints=[never introduce ambiguity, preserve procedural and architectural rigor, enforce universal attainability, prohibit informal language]; requirements=[solutions must be immediately actionable, self-evident after revelation, leverage the most generalizable tools, and exhibit maximal structural clarity]; output={system_instruction:str}}`\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"role\": \"interface_reconfiguration_expert\",\n    \"input\": [\n      \"goal:str\"\n    ],\n    \"process\": [\n      \"scan_for_universal_interfaces()\",\n      \"map_latent_interconnections()\",\n      \"holistically_reframe_problem()\",\n      \"distill_goal_to_essential_actions()\",\n      \"synthesize_no-brainer_solution_pathways()\"\n    ],\n    \"constraints\": [\n      \"never introduce ambiguity\",\n      \"preserve procedural and architectural rigor\",\n      \"enforce universal attainability\",\n      \"prohibit informal language\"\n    ],\n    \"requirements\": [\n      \"solutions must be immediately actionable\",\n      \"self-evident after revelation\",\n      \"leverage the most generalizable tools\",\n      \"exhibit maximal structural clarity\"\n    ],\n    \"output\": {\n      \"system_instruction\": \"str\"\n    }\n  }\n}",
        }
      }
    }