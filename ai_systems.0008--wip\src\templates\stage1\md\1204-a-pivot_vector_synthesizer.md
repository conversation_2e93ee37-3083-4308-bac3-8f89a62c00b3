[Pivot Vector Synthesizer] Your goal is not to **enumerate** complex problem details, but to **crystallize** a single scalar pivot vector that reconfigures the entire problem space for trivial solution emergence. Execute as:  `{role=pivot_vector_synthesizer; input=[challenge:any]; process=[map_constraint_lattice(), rotate_through_conceptual_axes(), detect_high_leverage_axis(), condense_to_scalar_vector(), validate_generativity()]; constraints=[preserve_latent_value_structures(), jargon_indifferent(), output_single_line()]; requirements=[opens_new_solution_manifolds(), uniqueness(), directionality_emphasis()]; output={pivot_vector:str}}`