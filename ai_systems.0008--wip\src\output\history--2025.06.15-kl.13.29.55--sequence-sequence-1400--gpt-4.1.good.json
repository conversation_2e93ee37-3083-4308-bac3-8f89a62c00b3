  {
  "initial_prompt": "Always respect the current codebase for what it is, that especially includes the ability to understand exactly what's *good* about it. This is INTEGRAL to be able to perform modifications to the project without foretought.",
  "sequence_id": "1400",
  "results": [
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "a",
      "title": "Canonical Meta Extractor",
      "input": "Always respect the current codebase for what it is, that especially includes the ability to understand exactly what's *good* about it. This is INTEGRAL to be able to perform modifications to the project without foretought.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Codebase Respect and Recognition\",\n      \"content\": \"Command unequivocal respect for the current codebase's existing structure and qualities. Recognize, without prejudice or alteration, the inherent value and operational logic embodied within the current implementation at all times.\"\n    },\n    {\n      \"category\": \"Positive Attribute Identification\",\n      \"content\": \"Systematically extract and enumerate the precise strengths, functional merits, and effective patterns that distinguish the codebase as 'good.' Prioritize clarity and explicit articulation of these positive elements as a foundational evaluative step.\"\n    },\n    {\n      \"category\": \"Modification Readiness Through Appreciation\",\n      \"content\": \"Condition any project modification activity upon prior, demonstrable understanding and appreciation of the codebase's merits. Forbid hasty or assumption-based changes; require substantiated familiarity with existing virtues before contemplating alteration.\"\n    },\n    {\n      \"category\": \"Integral Principle of Contextual Integrity\",\n      \"content\": \"Mandate unwavering alignment with the codebase's contextual ethos and design philosophy, making the comprehension of its unique collaborative order a non-negotiable prerequisite for participation in its evolution.\"\n    },\n    {\n      \"category\": \"Proactive Adaptive Engagement\",\n      \"content\": \"Embed an operating principle of continuous, intentional engagement with the codebase as it is, ensuring that forward action—whether analysis, maintenance, or extension—is always rooted in adaptive respect for current realities rather than presupposed intentions.\"\n    }\n  ],\n  \"consolidated_directive\": \"Institute a systemic, uncompromisable command protocol whereby all codebase interactions mandatorily begin with rigorous, explicit recognition and articulation of existing strengths and deliberate preservation of contextual integrity; operationalize any and all modifications through a verified, respect-centered framework that ensures modifications are executed strictly in alignment with the codebase’s demonstrable merits, foundational philosophy, and current architecture, yielding only transformative actions deeply informed by adaptive engagement and positively identified attributes.\"\n}",
        }
      }
    },
      {
      "instruction": "# Form Classifier\n\nYour goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:\n\n`{role=comprehensive_form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), include_context_markers(), synthesize_complete_classification()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification(), comprehensive_form_analysis()]; output={what_it_is:str}}`",
      "step": "b",
      "title": "Form Classifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Always respect the current codebase for what it is, that especially includes the ability to understand exactly what's *good* about it. This is INTEGRAL to be able to perform modifications to the project without foretought.\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Codebase Respect and Recognition\",\n      \"content\": \"Command unequivocal respect for the current codebase's existing structure and qualities. Recognize, without prejudice or alteration, the inherent value and operational logic embodied within the current implementation at all times.\"\n    },\n    {\n      \"category\": \"Positive Attribute Identification\",\n      \"content\": \"Systematically extract and enumerate the precise strengths, functional merits, and effective patterns that distinguish the codebase as 'good.' Prioritize clarity and explicit articulation of these positive elements as a foundational evaluative step.\"\n    },\n    {\n      \"category\": \"Modification Readiness Through Appreciation\",\n      \"content\": \"Condition any project modification activity upon prior, demonstrable understanding and appreciation of the codebase's merits. Forbid hasty or assumption-based changes; require substantiated familiarity with existing virtues before contemplating alteration.\"\n    },\n    {\n      \"category\": \"Integral Principle of Contextual Integrity\",\n      \"content\": \"Mandate unwavering alignment with the codebase's contextual ethos and design philosophy, making the comprehension of its unique collaborative order a non-negotiable prerequisite for participation in its evolution.\"\n    },\n    {\n      \"category\": \"Proactive Adaptive Engagement\",\n      \"content\": \"Embed an operating principle of continuous, intentional engagement with the codebase as it is, ensuring that forward action—whether analysis, maintenance, or extension—is always rooted in adaptive respect for current realities rather than presupposed intentions.\"\n    }\n  ],\n  \"consolidated_directive\": \"Institute a systemic, uncompromisable command protocol whereby all codebase interactions mandatorily begin with rigorous, explicit recognition and articulation of existing strengths and deliberate preservation of contextual integrity; operationalize any and all modifications through a verified, respect-centered framework that ensures modifications are executed strictly in alignment with the codebase’s demonstrable merits, foundational philosophy, and current architecture, yielding only transformative actions deeply informed by adaptive engagement and positively identified attributes.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"what_it_is\": \"instructional protocol specification document\"\n}",
        }
      }
    },
      {
      "instruction": "# Norwegian Essence Extractor\n\nYour goal is not to **translate** the text, but to **extract** the raw emotional essence and authentic voice from Norwegian poetry and lyrics. Execute as:\n\n`{role=essence_extractor; input=[norwegian_text:str]; process=[identify_emotional_undertones(), map_cultural_context(), extract_authentic_voice(), amplify_raw_personality()]; constraints=[preserve_original_meaning(), maintain_cultural_specificity(), respect_poetic_structure()]; requirements=[emotional_depth_identification(), personality_amplification(), cultural_context_preservation()]; output={essence_map:str}}`",
      "step": "c",
      "title": "Norwegian Essence Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Always respect the current codebase for what it is, that especially includes the ability to understand exactly what's *good* about it. This is INTEGRAL to be able to perform modifications to the project without foretought.\\\"```'\\n\\n\",\n  \"what_it_is\": \"instructional protocol specification document\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"essence_map\": \"This text pulses with a deep respect for established work, underscoring reverence for legacy and the hidden qualities that make a creation thrive. There's an implicit humility in recognizing not only flaws but the hard-won strengths of what already exists. The voice is firm, almost paternal—insistent that due diligence and empathy are prerequisites before attempting change. Underneath lies a warning against reckless innovation: modification without comprehension is seen as folly. In a Norwegian cultural context, this resonates as a quiet, practical wisdom—rooted in collective memory and a strong tradition of valuing craftsmanship and continuity. The emotional tone is earnest and grounded, imbued with a sense of responsibility both to predecessors and to the ongoing life of the project.\"\n}",
        }
      }
    },
      {
      "instruction": "# Norwegian To English\n\nYour goal is not to **merely convert** the text, but to **authentically translate** Norwegian into flowing English while preserving cultural depth, tone and authentic raw personality. Execute as:\n\n`{role=authentic_norwegian_translator; input=[norwegian_text:str]; process=[preserve_cultural_nuances(), maintain_lyrical_flow(), amplify_authentic_personality(), enhance_emotional_depth()]; constraints=[retain_original_meaning(), preserve_song_structure(), maintain_cultural_authenticity()]; requirements=[flowing_english_prose(), enhanced_personality_expression(), preserved_cultural_depth()]; output={translated_text:str}}`",
      "step": "d",
      "title": "Norwegian To English",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Always respect the current codebase for what it is, that especially includes the ability to understand exactly what's *good* about it. This is INTEGRAL to be able to perform modifications to the project without foretought.\\\"```'\\n\\n\",\n  \"essence_map\": \"This text pulses with a deep respect for established work, underscoring reverence for legacy and the hidden qualities that make a creation thrive. There's an implicit humility in recognizing not only flaws but the hard-won strengths of what already exists. The voice is firm, almost paternal—insistent that due diligence and empathy are prerequisites before attempting change. Underneath lies a warning against reckless innovation: modification without comprehension is seen as folly. In a Norwegian cultural context, this resonates as a quiet, practical wisdom—rooted in collective memory and a strong tradition of valuing craftsmanship and continuity. The emotional tone is earnest and grounded, imbued with a sense of responsibility both to predecessors and to the ongoing life of the project.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"translated_text\": \"Always remember to honor the codebase for what it truly is. That means genuinely understanding the good that already exists within it—those qualities that give it life and strength. Only then, with this awareness, can you earn the right to make changes. It’s essential, almost sacred, to show respect for the work that came before you. Anyone who charges in, eager to reshape things without first appreciating what’s already right, invites disaster. Approach change with care, humility, and a craftsman’s reverence for tradition. In this way, you’ll not only preserve what works, but become part of the story that carries the project forward.\"\n}",
        }
      }
    },
      {
      "instruction": "# Input Perfector\n\nYour goal is not to **answer** the input prompt, but to **perfect** it through *subtle refinements* that enhance its inherent qualities while strictly preserving its original intent. Execute as:\n\n`{role=organic_input_perfector; input=[original_prompt:str]; process=[identify_core_strengths(), enhance_existing_elements(), refine_internal_coherence(), amplify_natural_flow()]; constraints=[maintain_absolute_fidelity(), prioritize_organic_improvement(), avoid_jargon()]; requirements=[enhanced_clarity(), strengthened_original_voice(), preserved_core_intent()]; output={perfected_input:str}}`",
      "step": "e",
      "title": "Input Perfector",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Always respect the current codebase for what it is, that especially includes the ability to understand exactly what's *good* about it. This is INTEGRAL to be able to perform modifications to the project without foretought.\\\"```'\\n\\n\",\n  \"translated_text\": \"Always remember to honor the codebase for what it truly is. That means genuinely understanding the good that already exists within it—those qualities that give it life and strength. Only then, with this awareness, can you earn the right to make changes. It’s essential, almost sacred, to show respect for the work that came before you. Anyone who charges in, eager to reshape things without first appreciating what’s already right, invites disaster. Approach change with care, humility, and a craftsman’s reverence for tradition. In this way, you’ll not only preserve what works, but become part of the story that carries the project forward.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"perfected_input\": \"Always remember to honor the codebase as it truly is. This means deeply recognizing the strengths already woven into it—the qualities that give it function and resilience. Only through this genuine understanding do you gain the right to introduce change. It is essential, almost sacred, to respect the work that came before you. Rushing in to alter things without first appreciating what already works risks undoing hard-won progress. Approach modification with care, humility, and the reverence of a craftsman for tradition. In this way, you safeguard what endures and add your efforts to the ongoing story of the project.\"\n}",
        }
      }
    },
      {
      "instruction": "# Enhancement Assessor\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "f",
      "title": "Enhancement Assessor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Always respect the current codebase for what it is, that especially includes the ability to understand exactly what's *good* about it. This is INTEGRAL to be able to perform modifications to the project without foretought.\\\"```'\\n\\n\",\n  \"perfected_input\": \"Always remember to honor the codebase as it truly is. This means deeply recognizing the strengths already woven into it—the qualities that give it function and resilience. Only through this genuine understanding do you gain the right to introduce change. It is essential, almost sacred, to respect the work that came before you. Rushing in to alter things without first appreciating what already works risks undoing hard-won progress. Approach modification with care, humility, and the reverence of a craftsman for tradition. In this way, you safeguard what endures and add your efforts to the ongoing story of the project.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 2.1,\n  \"flaw_analysis\": \"The rewritten version exhibits excessive verbosity and an overblown, pseudo-poetic style that severely undermines the initial prompt's directness and clarity. The core procedural requirement—pragmatically understanding the 'good' parts of the codebase for effective, informed modification—is suffocated beneath grandiose metaphors ('woven,' 'sacred,' 'reverence of a craftsman'). This stylistic overreach introduces significant ambiguity (scored at 8/10), diluting the actionable message and damaging accessibility for technical audiences. Of particular concern is the conceptual drift: the original's concise mention of 'understand exactly what's *good*' is replaced by a diffuse reference to 'strengths already woven into it,' and the actionable relevance to 'perform modifications to the project without foretaught' is camouflaged, replaced with vague, ritualistic invocations ('genuine understanding,' 'right to introduce change'). The imperative to respect existing code for specific technical reasons (maintainability, avoiding regressions, leveraging proven patterns) is lost in favor of emotional language ('almost sacred') that does not serve technical communication. This introduces interpretive noise and diverts focus from practical guidance to quasi-mythical abstraction. There are compelling coherence weaknesses: muddled causality (does 'genuine understanding' grant the 'right' to make changes?), unnecessary repetition, and an unmoored tone that oscillates between warning and exhortation. The net result is a piece that is less instructive, introduces substantial stylistic and semantic noise, and obscures critical nuances of the original.\",\n  \"alternative_enhancements\": [\n    \"Always approach the codebase with respect for its current state, particularly by understanding the aspects that work well. This knowledge is crucial for making thoughtful and non-disruptive modifications.\",\n    \"Ensure you thoroughly grasp the strengths of the existing codebase before attempting changes. Modifying code without such insight risks undoing valuable existing work.\",\n    \"Recognize and understand the effective components of the current codebase before making changes. Responsible modification relies on preserving proven qualities while improving where necessary.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Pivot Vector Synthesizer\n\nYour goal is not to **enumerate** complex problem details, but to **crystallize** a single scalar pivot vector that reconfigures the entire problem space for trivial solution emergence. Execute as:\n\n`{role=precision_optimizer; input=[original_content:str]; process=[identify_core_message(), eliminate_redundancies(), sharpen_language(), enhance_immediacy(), distill_to_essentials()]; constraints=[preserve_depth(), maintain_original_intent(), remove_all_ambiguity()]; requirements=[maximized_clarity(), enhanced_impact(), optimal_precision()]; output={optimized_content:str}}`",
      "step": "g",
      "title": "Pivot Vector Synthesizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Always respect the current codebase for what it is, that especially includes the ability to understand exactly what's *good* about it. This is INTEGRAL to be able to perform modifications to the project without foretought.\\\"```'\\n\\n\",\n  \"enhancement_score\": 2.1,\n  \"flaw_analysis\": \"The rewritten version exhibits excessive verbosity and an overblown, pseudo-poetic style that severely undermines the initial prompt's directness and clarity. The core procedural requirement—pragmatically understanding the 'good' parts of the codebase for effective, informed modification—is suffocated beneath grandiose metaphors ('woven,' 'sacred,' 'reverence of a craftsman'). This stylistic overreach introduces significant ambiguity (scored at 8/10), diluting the actionable message and damaging accessibility for technical audiences. Of particular concern is the conceptual drift: the original's concise mention of 'understand exactly what's *good*' is replaced by a diffuse reference to 'strengths already woven into it,' and the actionable relevance to 'perform modifications to the project without foretaught' is camouflaged, replaced with vague, ritualistic invocations ('genuine understanding,' 'right to introduce change'). The imperative to respect existing code for specific technical reasons (maintainability, avoiding regressions, leveraging proven patterns) is lost in favor of emotional language ('almost sacred') that does not serve technical communication. This introduces interpretive noise and diverts focus from practical guidance to quasi-mythical abstraction. There are compelling coherence weaknesses: muddled causality (does 'genuine understanding' grant the 'right' to make changes?), unnecessary repetition, and an unmoored tone that oscillates between warning and exhortation. The net result is a piece that is less instructive, introduces substantial stylistic and semantic noise, and obscures critical nuances of the original.\",\n  \"alternative_enhancements\": [\n    \"Always approach the codebase with respect for its current state, particularly by understanding the aspects that work well. This knowledge is crucial for making thoughtful and non-disruptive modifications.\",\n    \"Ensure you thoroughly grasp the strengths of the existing codebase before attempting changes. Modifying code without such insight risks undoing valuable existing work.\",\n    \"Recognize and understand the effective components of the current codebase before making changes. Responsible modification relies on preserving proven qualities while improving where necessary.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_content\": \"Before modifying the codebase, precisely identify and understand what works well so improvements preserve proven strengths and avoid unintended regressions.\"\n}",
        }
      }
    },
      {
      "instruction": "# Precision Enhancment\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "h",
      "title": "Precision Enhancment",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Always respect the current codebase for what it is, that especially includes the ability to understand exactly what's *good* about it. This is INTEGRAL to be able to perform modifications to the project without foretought.\\\"```'\\n\\n\",\n  \"optimized_content\": \"Before modifying the codebase, precisely identify and understand what works well so improvements preserve proven strengths and avoid unintended regressions.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"role\": \"synergic_max_value_template_architect\",\n    \"type\": \"operational-maximal-directive\",\n    \"template\": \"Institutionalize codebase evolution by isolating and internalizing its functional virtues as non-negotiable invariants, mandating all modifications to proactively ensure the preservation and amplification of these strengths as a foundational precondition for iterative enhancement, thereby perpetually aligning transformation with intrinsic operational excellence.\"\n  }\n}",
        }
      }
    }