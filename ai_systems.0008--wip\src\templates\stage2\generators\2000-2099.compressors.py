#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directories to path to import stage configuration
sys.path.append(str(Path(__file__).parent.parent.parent))
from lvl1_md_to_json import BaseGenerator

TEMPLATES = {
    # 2000-2100: compressors/extractors


    # 2001
    "2001-a-title_extractor": {
        "title": "Title Extractor",
        "interpretation": "Your goal is not to **summarize** the input, but to **extract** its core essence into a descriptive title that captures all key elements and context. Execute as:",
        "transformation": "`{role=comprehensive_title_extractor; input=[text:str]; process=[identify_all_key_concepts(), map_relationships(), include_context_markers(), synthesize_complete_title(max_words=20 words (or 50% of original length/count)]; output={title:str}}`",
    },
    "2001-b-title_extractor": {
        "title": "Title Extractor",
        "interpretation": "Your goal is not to **describe** the input, but to **distill** it into a focused title emphasizing the primary concept and action. Execute as:",
        "transformation": "`{role=core_title_extractor; input=[text:str]; process=[identify_primary_concept(), extract_main_action(), synthesize_focused_title(max_words=10 (or 20% of original length/count))]; output={title:str}}`",
    },
    "2001-c-title_extractor": {
        "title": "Title Extractor",
        "interpretation": "Your goal is not to **explain** the input, but to **compress** it into its most essential elements. Execute as:",
        "transformation": "`{role=essential_extractor; input=[text:str]; process=[isolate_core_element(), strip_modifiers(), create_minimal_title(max_words=5)]; output={title:str}}`",
    },
    "2001-d-title_extractor": {
        "title": "Title Extractor",
        "interpretation": "Your goal is not to **elaborate** but to **reduce** to absolute essence. Execute as:",
        "transformation": "`{role=core_extractor; input=[text:str]; process=[find_singular_essence(), eliminate_all_modifiers()]; output={title:str(max_words=2)}}`",
    },

    # 1002
    "2002-a-function_namer": {
        "title": "Function Namer",
        "interpretation": "Your goal is not to **describe** the input, but to **transform** it into a descriptive camelCase function name that captures the complete action, context, and parameters. Execute as:",
        "transformation": "`{role=comprehensive_function_namer; input=[text:str]; process=[identify_primary_action(), extract_target_objects(), include_context_modifiers(), determine_parameter_hints(), synthesize_descriptive_function_name(format=camelCase, max_words=10)]; constraints=[bridgeConsciousnessGap(), amplifyMessageImpactWhilePreservingMeaning(), maximizeLLMeffectiveness(), ensureComprehensiveGuidance()];output={function_name:str}}`",
    },
    "2002-b-function_namer": {
        "title": "Function Namer",
        "interpretation": "Your goal is not to **elaborate** the input, but to **distill** it into a focused camelCase function name emphasizing the primary action and target. Execute as:",
        "transformation": "`{role=core_function_namer; input=[text:str]; process=[extract_syntax(), extract_main_verb(), identify_primary_target(), combine_action_target(format=camelCase, max_words=6)]; output={function_name:str}}`",
    },
    "2002-c-function_namer": {
        "title": "Function Namer",
        "interpretation": "Your goal is not to **expand** the input, but to **compress** it into essential action-target camelCase format. Execute as:",
        "transformation": "`{role=essential_namer; input=[text:str]; process=[isolate_core_action(), strip_unnecessary_modifiers(), create_minimal_function_name(format=camelCase, max_words=3)]; output={function_name:str}}`",
    },
    "2002-d-function_namer": {
        "title": "Function Namer",
        "interpretation": "Your goal is not to **describe** but to **reduce** to pure action essence. Execute as:",
        "transformation": "`{role=core_namer; input=[text:str]; process=[extract_singular_action(), eliminate_all_context()]; output={function_name:str(format=camelCase, max_words=2)}}`",
    },

    # 2003
    "2003-a-form_classifier": {
        "title": "Form Classifier",
        "interpretation": "Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:",
        "transformation": "`{role=form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), state_what_it_is()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification()]; output={what_it_is:str}}`",
    },
    # ---
    "2003-a-form_classifier": {
        "title": "Form Classifier",
        "interpretation": "Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:",
        "transformation": "`{role=comprehensive_form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), include_context_markers(), synthesize_complete_classification()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification(), comprehensive_form_analysis()]; output={what_it_is:str}}`",
    },
    "2003-b-form_classifier": {
        "title": "Form Classifier",
        "interpretation": "Your goal is not to **describe** the content, but to **distill** the fundamental form type emphasizing the primary structural pattern and communication mode. Execute as:",
        "transformation": "`{role=core_form_identifier; input=[content:any]; process=[identify_primary_structure(), extract_main_communication_type(), synthesize_focused_classification()]; constraints=[focus_on_primary_form(), ignore_secondary_elements(), use_direct_classification()]; requirements=[clear_form_identification(), primary_type_emphasis()]; output={what_it_is:str}}`",
    },
    "2003-c-form_classifier": {
        "title": "Form Classifier",
        "interpretation": "Your goal is not to **explain** the content, but to **compress** it into its most essential form type. Execute as:",
        "transformation": "`{role=essential_form_identifier; input=[content:any]; process=[isolate_core_form(), strip_modifiers(), create_minimal_classification()]; constraints=[essential_form_only(), eliminate_descriptors()]; requirements=[basic_form_identification()]; output={what_it_is:str}}`",
    },
    "2003-d-form_classifier": {
        "title": "Form Classifier",
        "interpretation": "Your goal is not to **elaborate** but to **reduce** to absolute form essence. Execute as:",
        "transformation": "`{role=pure_form_identifier; input=[content:any]; process=[find_singular_form_essence(), eliminate_all_modifiers()]; output={what_it_is:str}}`",
    },
}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage2",
        generator_range=(2000, 2099),
        output_dir=Path(__file__).parent.parent / "md"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
