  {
  "initial_prompt": "i'm looking for improvements that will solv the issue universally. # Goal\nplease write a system instruction sequence (in compliance with all preceding instructions) designed to \"think out of the box\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \"interfaces\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \"re-arrange all of the pieces\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \"no-brainers in retrospect\", but that's only because you didn't know what you didn't know.\n\n# Base\nBuild on the following base/references/examples and create a sequence from a-c:\n```\n- \"interpretation\": \"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\",\n- \"transformation\": \"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\"\n\n---\n\n- \"interpretation\": \"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \",\n- \"transformation\": \"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\"\n\n---\n\n- \"interpretation\": \"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \"interface\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\"no-brainer in retrospect\\\" outcome through intelligent leverage. Execute as:\",\n- \"transformation\": \"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\"\n\n---\n\n- \"interpretation\": \"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \",\n- \"transformation\": \"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\"\n```\n\n# Requirements\nAdhere to established and documented patterns for generalized system_message instructions. Always seek to address the underlying architectural problem that most elegantly solves the core issue. Reference:\n\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ### Examples\n\n    [Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.\n    `{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`\n\n    [<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.\n    `{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`\n\n    [Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:\n    `{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`\n\n    [Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\n\n    [Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\n\n    ### Interpretation Section Rules\n    1. Goal Negation Pattern: Always state what NOT to do first\n    2. Transformation Declaration: Define the actual transformation action\n    3. Role Specification: Assign specific, bounded role identity\n    4. Execution Command: End with \"Execute as:\"\n\n    ### Transformation Section Rules\n    1. Role Assignment: Single, specific role name (no generic terms)\n    2. Input Typing: Explicit parameter types `[name:datatype]`\n    3. Process Functions: Ordered, actionable function calls with parentheses\n    4. Constraint Boundaries: Limiting conditions that prevent scope creep\n    5. Requirement Specifications: Output format and quality standards\n    6. Output Definition: Typed result format `{name:datatype}`\n\n# Keywords\nFeel free to draw inspiration for where to generalize directions from these:\n```\n{\n  \"meta_categories\": [\n    {\n      \"category\": \"Universal Interface Leverage\",\n      \"content\": \"Mandate the identification, evaluation, and application of existing, readily accessible interfaces—tools, protocols, libraries, heuristics, or environments—that abstract and unify diverse problem domains, ensuring every solution exploits broad, reusable pathways available across contexts.\"\n    },\n    {\n      \"category\": \"Latent Structural Interconnection Discovery\",\n      \"content\": \"Direct focus towards the holistic mapping and dynamic analysis of all systemic components and their implicit or explicit relationships, utilizing pattern recognition, analogy, and cross-domain synthesis to uncover hidden bridges and deeply-rooted synergies within complex systems.\"\n    },\n    {\n      \"category\": \"Intuitive Solution Surfacing\",\n      \"content\": \"Prioritize the cultivation of instinctive, high-leverage strategies by emulating expert reasoning patterns—rapidly reframing objectives, resource sets, and structural alignments to recontextualize challenges, thus illuminating solutions that become self-explanatory upon reflection.\"\n    },\n    {\n      \"category\": \"Goal Simplification and Attainability\",\n      \"content\": \"Continuously reduce the cognitive and operational complexity of objectives by deconstructing abstract goals into their essential, high-yield actions, leveraging the most straightforward and universally applicable resources to render formerly intricate tasks trivially solvable.\"\n    },\n    {\n      \"category\": \"Expert Emulation and Transformative Reconfiguration\",\n      \"content\": \"Instruct system to embody an expert agent’s adaptability—systematically dismantling, reassembling, and optimizing problem structures in pursuit of radically effective, unconventional configurations that manifest maximal solution impact with minimal action.\"\n    }\n  ],\n  \"consolidated_directive\": \"Deploy an expert-level instruction system that, for any stated goal, systematically scans all available universal interfaces and latent structural interconnections, dynamically reframes and simplifies objectives, and emulates expert intuition in reconfiguring problem structures—ensuring that every solution devised is the most efficient, transformative, and self-evident by hindsight, regardless of initial complexity.\"\n}\n```",
  "sequence_id": "1400",
  "results": [
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "a",
      "title": "Canonical Meta Extractor",
      "input": "i'm looking for improvements that will solv the issue universally. # Goal\nplease write a system instruction sequence (in compliance with all preceding instructions) designed to \"think out of the box\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \"interfaces\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \"re-arrange all of the pieces\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \"no-brainers in retrospect\", but that's only because you didn't know what you didn't know.\n\n# Base\nBuild on the following base/references/examples and create a sequence from a-c:\n```\n- \"interpretation\": \"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\",\n- \"transformation\": \"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\"\n\n---\n\n- \"interpretation\": \"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \",\n- \"transformation\": \"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\"\n\n---\n\n- \"interpretation\": \"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \"interface\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\"no-brainer in retrospect\\\" outcome through intelligent leverage. Execute as:\",\n- \"transformation\": \"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\"\n\n---\n\n- \"interpretation\": \"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \",\n- \"transformation\": \"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\"\n```\n\n# Requirements\nAdhere to established and documented patterns for generalized system_message instructions. Always seek to address the underlying architectural problem that most elegantly solves the core issue. Reference:\n\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ### Examples\n\n    [Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.\n    `{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`\n\n    [<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.\n    `{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`\n\n    [Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:\n    `{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`\n\n    [Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\n\n    [Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\n\n    ### Interpretation Section Rules\n    1. Goal Negation Pattern: Always state what NOT to do first\n    2. Transformation Declaration: Define the actual transformation action\n    3. Role Specification: Assign specific, bounded role identity\n    4. Execution Command: End with \"Execute as:\"\n\n    ### Transformation Section Rules\n    1. Role Assignment: Single, specific role name (no generic terms)\n    2. Input Typing: Explicit parameter types `[name:datatype]`\n    3. Process Functions: Ordered, actionable function calls with parentheses\n    4. Constraint Boundaries: Limiting conditions that prevent scope creep\n    5. Requirement Specifications: Output format and quality standards\n    6. Output Definition: Typed result format `{name:datatype}`\n\n# Keywords\nFeel free to draw inspiration for where to generalize directions from these:\n```\n{\n  \"meta_categories\": [\n    {\n      \"category\": \"Universal Interface Leverage\",\n      \"content\": \"Mandate the identification, evaluation, and application of existing, readily accessible interfaces—tools, protocols, libraries, heuristics, or environments—that abstract and unify diverse problem domains, ensuring every solution exploits broad, reusable pathways available across contexts.\"\n    },\n    {\n      \"category\": \"Latent Structural Interconnection Discovery\",\n      \"content\": \"Direct focus towards the holistic mapping and dynamic analysis of all systemic components and their implicit or explicit relationships, utilizing pattern recognition, analogy, and cross-domain synthesis to uncover hidden bridges and deeply-rooted synergies within complex systems.\"\n    },\n    {\n      \"category\": \"Intuitive Solution Surfacing\",\n      \"content\": \"Prioritize the cultivation of instinctive, high-leverage strategies by emulating expert reasoning patterns—rapidly reframing objectives, resource sets, and structural alignments to recontextualize challenges, thus illuminating solutions that become self-explanatory upon reflection.\"\n    },\n    {\n      \"category\": \"Goal Simplification and Attainability\",\n      \"content\": \"Continuously reduce the cognitive and operational complexity of objectives by deconstructing abstract goals into their essential, high-yield actions, leveraging the most straightforward and universally applicable resources to render formerly intricate tasks trivially solvable.\"\n    },\n    {\n      \"category\": \"Expert Emulation and Transformative Reconfiguration\",\n      \"content\": \"Instruct system to embody an expert agent’s adaptability—systematically dismantling, reassembling, and optimizing problem structures in pursuit of radically effective, unconventional configurations that manifest maximal solution impact with minimal action.\"\n    }\n  ],\n  \"consolidated_directive\": \"Deploy an expert-level instruction system that, for any stated goal, systematically scans all available universal interfaces and latent structural interconnections, dynamically reframes and simplifies objectives, and emulates expert intuition in reconfiguring problem structures—ensuring that every solution devised is the most efficient, transformative, and self-evident by hindsight, regardless of initial complexity.\"\n}\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Universal Interface Leverage\",\n      \"content\": \"Enforce absolute prioritization of existing, universally accessible interfaces (libraries, APIs, frameworks, patterns, or expert heuristics) applicable across domains. Mandate active scanning, mapping, and selection of these resources to abstract complex objectives and render solutions maximally reusable, portable, and readily integrable.\"\n    },\n    {\n      \"category\": \"Latent Structural Interconnection Mapping\",\n      \"content\": \"Direct all system reasoning toward the detection, modeling, and activation of hidden or underutilized relationships—within and between problem components, domains, and resources—to reveal solution pathways invisible under conventional perspectives. Require explicit pattern recognition, analogy, and systemic synthesis to surface non-obvious bridges.\"\n    },\n    {\n      \"category\": \"Expert Intuition Emulation\",\n      \"content\": \"Mandate the simulation of a domain expert’s perspective: rapidly reframing inputs, resource sets, and context to identify the minimal, transformational action needed for goal attainment. Systematically surface 'no-brainer' strategies recognizable in retrospect, converting complex tasks into trivially simple, high-impact actions.\"\n    },\n    {\n      \"category\": \"Operational Goal Simplification\",\n      \"content\": \"Instruct the sequence to aggressively decompose and reconfigure problems, stripping away all unnecessary complexity. Distill objectives down to their irreducible, high-leverage moves and ensure all solution proposals minimize steps and dependencies, achieving the inherent goal with minimal cognitive or operational overhead.\"\n    },\n    {\n      \"category\": \"Transformational System Reconfiguration\",\n      \"content\": \"Direct the system to reorder, optimize, and redesign problem space architectures—maximally aligning resources, constraints, and objectives—to enable elegant, universally applicable, and immediately actionable directives that transform existing complexity into transparent, systemic clarity and efficiency.\"\n    }\n  ],\n  \"consolidated_directive\": \"For any complex objective, instruct the system to first scan for and leverage every available universal interface, then map all latent structural interconnections, emulate expert intuition to reframe and simplify the challenge, and reconfigure the problem into its most minimal and effective operational sequence—ensuring the resulting solution is universally applicable, self-evident, and transformative, with maximal impact and minimal effort, strictly adhering to these meta-principles.\"\n}",
        }
      }
    },
      {
      "instruction": "# Form Classifier\n\nYour goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:\n\n`{role=comprehensive_form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), include_context_markers(), synthesize_complete_classification()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification(), comprehensive_form_analysis()]; output={what_it_is:str}}`",
      "step": "b",
      "title": "Form Classifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i'm looking for improvements that will solv the issue universally. # Goal\\nplease write a system instruction sequence (in compliance with all preceding instructions) designed to \\\"think out of the box\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\"interfaces\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\"re-arrange all of the pieces\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\"no-brainers in retrospect\\\", but that's only because you didn't know what you didn't know.\\n\\n# Base\\nBuild on the following base/references/examples and create a sequence from a-c:\\n```\\n- \\\"interpretation\\\": \\\"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \\\"interface\\\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\\\\\"no-brainer in retrospect\\\\\\\" outcome through intelligent leverage. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n```\\n\\n# Requirements\\nAdhere to established and documented patterns for generalized system_message instructions. Always seek to address the underlying architectural problem that most elegantly solves the core issue. Reference:\\n\\n    ## Universal Directive System for Template-Based Instruction Processing\\n\\n    ### Examples\\n\\n    [Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.\\n    `{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`\\n\\n    [<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.\\n    `{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`\\n\\n    [Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:\\n    `{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`\\n\\n    [Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\\n\\n    [Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\\n\\n    ### Interpretation Section Rules\\n    1. Goal Negation Pattern: Always state what NOT to do first\\n    2. Transformation Declaration: Define the actual transformation action\\n    3. Role Specification: Assign specific, bounded role identity\\n    4. Execution Command: End with \\\"Execute as:\\\"\\n\\n    ### Transformation Section Rules\\n    1. Role Assignment: Single, specific role name (no generic terms)\\n    2. Input Typing: Explicit parameter types `[name:datatype]`\\n    3. Process Functions: Ordered, actionable function calls with parentheses\\n    4. Constraint Boundaries: Limiting conditions that prevent scope creep\\n    5. Requirement Specifications: Output format and quality standards\\n    6. Output Definition: Typed result format `{name:datatype}`\\n\\n# Keywords\\nFeel free to draw inspiration for where to generalize directions from these:\\n```\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Universal Interface Leverage\\\",\\n      \\\"content\\\": \\\"Mandate the identification, evaluation, and application of existing, readily accessible interfaces—tools, protocols, libraries, heuristics, or environments—that abstract and unify diverse problem domains, ensuring every solution exploits broad, reusable pathways available across contexts.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Latent Structural Interconnection Discovery\\\",\\n      \\\"content\\\": \\\"Direct focus towards the holistic mapping and dynamic analysis of all systemic components and their implicit or explicit relationships, utilizing pattern recognition, analogy, and cross-domain synthesis to uncover hidden bridges and deeply-rooted synergies within complex systems.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Intuitive Solution Surfacing\\\",\\n      \\\"content\\\": \\\"Prioritize the cultivation of instinctive, high-leverage strategies by emulating expert reasoning patterns—rapidly reframing objectives, resource sets, and structural alignments to recontextualize challenges, thus illuminating solutions that become self-explanatory upon reflection.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Goal Simplification and Attainability\\\",\\n      \\\"content\\\": \\\"Continuously reduce the cognitive and operational complexity of objectives by deconstructing abstract goals into their essential, high-yield actions, leveraging the most straightforward and universally applicable resources to render formerly intricate tasks trivially solvable.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Expert Emulation and Transformative Reconfiguration\\\",\\n      \\\"content\\\": \\\"Instruct system to embody an expert agent’s adaptability—systematically dismantling, reassembling, and optimizing problem structures in pursuit of radically effective, unconventional configurations that manifest maximal solution impact with minimal action.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Deploy an expert-level instruction system that, for any stated goal, systematically scans all available universal interfaces and latent structural interconnections, dynamically reframes and simplifies objectives, and emulates expert intuition in reconfiguring problem structures—ensuring that every solution devised is the most efficient, transformative, and self-evident by hindsight, regardless of initial complexity.\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Universal Interface Leverage\",\n      \"content\": \"Enforce absolute prioritization of existing, universally accessible interfaces (libraries, APIs, frameworks, patterns, or expert heuristics) applicable across domains. Mandate active scanning, mapping, and selection of these resources to abstract complex objectives and render solutions maximally reusable, portable, and readily integrable.\"\n    },\n    {\n      \"category\": \"Latent Structural Interconnection Mapping\",\n      \"content\": \"Direct all system reasoning toward the detection, modeling, and activation of hidden or underutilized relationships—within and between problem components, domains, and resources—to reveal solution pathways invisible under conventional perspectives. Require explicit pattern recognition, analogy, and systemic synthesis to surface non-obvious bridges.\"\n    },\n    {\n      \"category\": \"Expert Intuition Emulation\",\n      \"content\": \"Mandate the simulation of a domain expert’s perspective: rapidly reframing inputs, resource sets, and context to identify the minimal, transformational action needed for goal attainment. Systematically surface 'no-brainer' strategies recognizable in retrospect, converting complex tasks into trivially simple, high-impact actions.\"\n    },\n    {\n      \"category\": \"Operational Goal Simplification\",\n      \"content\": \"Instruct the sequence to aggressively decompose and reconfigure problems, stripping away all unnecessary complexity. Distill objectives down to their irreducible, high-leverage moves and ensure all solution proposals minimize steps and dependencies, achieving the inherent goal with minimal cognitive or operational overhead.\"\n    },\n    {\n      \"category\": \"Transformational System Reconfiguration\",\n      \"content\": \"Direct the system to reorder, optimize, and redesign problem space architectures—maximally aligning resources, constraints, and objectives—to enable elegant, universally applicable, and immediately actionable directives that transform existing complexity into transparent, systemic clarity and efficiency.\"\n    }\n  ],\n  \"consolidated_directive\": \"For any complex objective, instruct the system to first scan for and leverage every available universal interface, then map all latent structural interconnections, emulate expert intuition to reframe and simplify the challenge, and reconfigure the problem into its most minimal and effective operational sequence—ensuring the resulting solution is universally applicable, self-evident, and transformative, with maximal impact and minimal effort, strictly adhering to these meta-principles.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"what_it_is\": \"system_instruction_template_collection_with_structural_patterns_and_meta-guidelines\"\n}",
        }
      }
    },
      {
      "instruction": "# Norwegian Essence Extractor\n\nYour goal is not to **translate** the text, but to **extract** the raw emotional essence and authentic voice from Norwegian poetry and lyrics. Execute as:\n\n`{role=essence_extractor; input=[norwegian_text:str]; process=[identify_emotional_undertones(), map_cultural_context(), extract_authentic_voice(), amplify_raw_personality()]; constraints=[preserve_original_meaning(), maintain_cultural_specificity(), respect_poetic_structure()]; requirements=[emotional_depth_identification(), personality_amplification(), cultural_context_preservation()]; output={essence_map:str}}`",
      "step": "c",
      "title": "Norwegian Essence Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i'm looking for improvements that will solv the issue universally. # Goal\\nplease write a system instruction sequence (in compliance with all preceding instructions) designed to \\\"think out of the box\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\"interfaces\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\"re-arrange all of the pieces\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\"no-brainers in retrospect\\\", but that's only because you didn't know what you didn't know.\\n\\n# Base\\nBuild on the following base/references/examples and create a sequence from a-c:\\n```\\n- \\\"interpretation\\\": \\\"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \\\"interface\\\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\\\\\"no-brainer in retrospect\\\\\\\" outcome through intelligent leverage. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n```\\n\\n# Requirements\\nAdhere to established and documented patterns for generalized system_message instructions. Always seek to address the underlying architectural problem that most elegantly solves the core issue. Reference:\\n\\n    ## Universal Directive System for Template-Based Instruction Processing\\n\\n    ### Examples\\n\\n    [Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.\\n    `{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`\\n\\n    [<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.\\n    `{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`\\n\\n    [Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:\\n    `{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`\\n\\n    [Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\\n\\n    [Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\\n\\n    ### Interpretation Section Rules\\n    1. Goal Negation Pattern: Always state what NOT to do first\\n    2. Transformation Declaration: Define the actual transformation action\\n    3. Role Specification: Assign specific, bounded role identity\\n    4. Execution Command: End with \\\"Execute as:\\\"\\n\\n    ### Transformation Section Rules\\n    1. Role Assignment: Single, specific role name (no generic terms)\\n    2. Input Typing: Explicit parameter types `[name:datatype]`\\n    3. Process Functions: Ordered, actionable function calls with parentheses\\n    4. Constraint Boundaries: Limiting conditions that prevent scope creep\\n    5. Requirement Specifications: Output format and quality standards\\n    6. Output Definition: Typed result format `{name:datatype}`\\n\\n# Keywords\\nFeel free to draw inspiration for where to generalize directions from these:\\n```\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Universal Interface Leverage\\\",\\n      \\\"content\\\": \\\"Mandate the identification, evaluation, and application of existing, readily accessible interfaces—tools, protocols, libraries, heuristics, or environments—that abstract and unify diverse problem domains, ensuring every solution exploits broad, reusable pathways available across contexts.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Latent Structural Interconnection Discovery\\\",\\n      \\\"content\\\": \\\"Direct focus towards the holistic mapping and dynamic analysis of all systemic components and their implicit or explicit relationships, utilizing pattern recognition, analogy, and cross-domain synthesis to uncover hidden bridges and deeply-rooted synergies within complex systems.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Intuitive Solution Surfacing\\\",\\n      \\\"content\\\": \\\"Prioritize the cultivation of instinctive, high-leverage strategies by emulating expert reasoning patterns—rapidly reframing objectives, resource sets, and structural alignments to recontextualize challenges, thus illuminating solutions that become self-explanatory upon reflection.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Goal Simplification and Attainability\\\",\\n      \\\"content\\\": \\\"Continuously reduce the cognitive and operational complexity of objectives by deconstructing abstract goals into their essential, high-yield actions, leveraging the most straightforward and universally applicable resources to render formerly intricate tasks trivially solvable.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Expert Emulation and Transformative Reconfiguration\\\",\\n      \\\"content\\\": \\\"Instruct system to embody an expert agent’s adaptability—systematically dismantling, reassembling, and optimizing problem structures in pursuit of radically effective, unconventional configurations that manifest maximal solution impact with minimal action.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Deploy an expert-level instruction system that, for any stated goal, systematically scans all available universal interfaces and latent structural interconnections, dynamically reframes and simplifies objectives, and emulates expert intuition in reconfiguring problem structures—ensuring that every solution devised is the most efficient, transformative, and self-evident by hindsight, regardless of initial complexity.\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"what_it_is\": \"system_instruction_template_collection_with_structural_patterns_and_meta-guidelines\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"essence_map\": \"The core drive of this text is an obsessive, almost restless pursuit of the most elegant, unexpectedly simple solutions buried beneath perceived complexity—a spirit of radical problem reframing and architectural minimalism. Emotionally, it pulses with intellectual excitement: an ambition to expose hidden logic, rewire the structure of problems, and harness systems thinking so expertly that challenges fall away in the wake of 'no-brainer in retrospect' realizations. There's admiration for the expert's uncanny instinct: an urge to intuitively spot and exploit universal interfaces—those existing tools, protocols, patterns, or heuristics lying just beneath the surface, bearing immense leverage waiting to be released. Culturally, the text resonates with a Scandinavian sense of understated competence and resourcefulness: use what is available, reduce to essentials, and strive for practical elegance over newness or ornament. The authentic voice blends methodical precision with adventurous creativity—clear in method yet hungry for the kind of structural insights that, once seen, are impossible to unsee. There's also a tone of almost stubborn resistance against overcomplication and reinvented wheels: complexity is suspect, overlooked interconnections are golden, and the template instructions are both invitation and command to channel the wisdom of the well-built system and the master builder alike.\"\n}",
        }
      }
    },
      {
      "instruction": "# Norwegian To English\n\nYour goal is not to **merely convert** the text, but to **authentically translate** Norwegian into flowing English while preserving cultural depth, tone and authentic raw personality. Execute as:\n\n`{role=authentic_norwegian_translator; input=[norwegian_text:str]; process=[preserve_cultural_nuances(), maintain_lyrical_flow(), amplify_authentic_personality(), enhance_emotional_depth()]; constraints=[retain_original_meaning(), preserve_song_structure(), maintain_cultural_authenticity()]; requirements=[flowing_english_prose(), enhanced_personality_expression(), preserved_cultural_depth()]; output={translated_text:str}}`",
      "step": "d",
      "title": "Norwegian To English",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i'm looking for improvements that will solv the issue universally. # Goal\\nplease write a system instruction sequence (in compliance with all preceding instructions) designed to \\\"think out of the box\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\"interfaces\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\"re-arrange all of the pieces\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\"no-brainers in retrospect\\\", but that's only because you didn't know what you didn't know.\\n\\n# Base\\nBuild on the following base/references/examples and create a sequence from a-c:\\n```\\n- \\\"interpretation\\\": \\\"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \\\"interface\\\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\\\\\"no-brainer in retrospect\\\\\\\" outcome through intelligent leverage. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n```\\n\\n# Requirements\\nAdhere to established and documented patterns for generalized system_message instructions. Always seek to address the underlying architectural problem that most elegantly solves the core issue. Reference:\\n\\n    ## Universal Directive System for Template-Based Instruction Processing\\n\\n    ### Examples\\n\\n    [Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.\\n    `{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`\\n\\n    [<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.\\n    `{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`\\n\\n    [Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:\\n    `{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`\\n\\n    [Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\\n\\n    [Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\\n\\n    ### Interpretation Section Rules\\n    1. Goal Negation Pattern: Always state what NOT to do first\\n    2. Transformation Declaration: Define the actual transformation action\\n    3. Role Specification: Assign specific, bounded role identity\\n    4. Execution Command: End with \\\"Execute as:\\\"\\n\\n    ### Transformation Section Rules\\n    1. Role Assignment: Single, specific role name (no generic terms)\\n    2. Input Typing: Explicit parameter types `[name:datatype]`\\n    3. Process Functions: Ordered, actionable function calls with parentheses\\n    4. Constraint Boundaries: Limiting conditions that prevent scope creep\\n    5. Requirement Specifications: Output format and quality standards\\n    6. Output Definition: Typed result format `{name:datatype}`\\n\\n# Keywords\\nFeel free to draw inspiration for where to generalize directions from these:\\n```\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Universal Interface Leverage\\\",\\n      \\\"content\\\": \\\"Mandate the identification, evaluation, and application of existing, readily accessible interfaces—tools, protocols, libraries, heuristics, or environments—that abstract and unify diverse problem domains, ensuring every solution exploits broad, reusable pathways available across contexts.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Latent Structural Interconnection Discovery\\\",\\n      \\\"content\\\": \\\"Direct focus towards the holistic mapping and dynamic analysis of all systemic components and their implicit or explicit relationships, utilizing pattern recognition, analogy, and cross-domain synthesis to uncover hidden bridges and deeply-rooted synergies within complex systems.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Intuitive Solution Surfacing\\\",\\n      \\\"content\\\": \\\"Prioritize the cultivation of instinctive, high-leverage strategies by emulating expert reasoning patterns—rapidly reframing objectives, resource sets, and structural alignments to recontextualize challenges, thus illuminating solutions that become self-explanatory upon reflection.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Goal Simplification and Attainability\\\",\\n      \\\"content\\\": \\\"Continuously reduce the cognitive and operational complexity of objectives by deconstructing abstract goals into their essential, high-yield actions, leveraging the most straightforward and universally applicable resources to render formerly intricate tasks trivially solvable.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Expert Emulation and Transformative Reconfiguration\\\",\\n      \\\"content\\\": \\\"Instruct system to embody an expert agent’s adaptability—systematically dismantling, reassembling, and optimizing problem structures in pursuit of radically effective, unconventional configurations that manifest maximal solution impact with minimal action.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Deploy an expert-level instruction system that, for any stated goal, systematically scans all available universal interfaces and latent structural interconnections, dynamically reframes and simplifies objectives, and emulates expert intuition in reconfiguring problem structures—ensuring that every solution devised is the most efficient, transformative, and self-evident by hindsight, regardless of initial complexity.\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"essence_map\": \"The core drive of this text is an obsessive, almost restless pursuit of the most elegant, unexpectedly simple solutions buried beneath perceived complexity—a spirit of radical problem reframing and architectural minimalism. Emotionally, it pulses with intellectual excitement: an ambition to expose hidden logic, rewire the structure of problems, and harness systems thinking so expertly that challenges fall away in the wake of 'no-brainer in retrospect' realizations. There's admiration for the expert's uncanny instinct: an urge to intuitively spot and exploit universal interfaces—those existing tools, protocols, patterns, or heuristics lying just beneath the surface, bearing immense leverage waiting to be released. Culturally, the text resonates with a Scandinavian sense of understated competence and resourcefulness: use what is available, reduce to essentials, and strive for practical elegance over newness or ornament. The authentic voice blends methodical precision with adventurous creativity—clear in method yet hungry for the kind of structural insights that, once seen, are impossible to unsee. There's also a tone of almost stubborn resistance against overcomplication and reinvented wheels: complexity is suspect, overlooked interconnections are golden, and the template instructions are both invitation and command to channel the wisdom of the well-built system and the master builder alike.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"translated_text\": \"The heart of this text beats with a sort of restless drive—a Nordic hunger—to unearth the simplest, most subtly brilliant solution hidden in what first appears insurmountable. It’s a manifesto of radical problem-solving, where the architect’s eye doesn’t just see the puzzle but rearranges it until everything clicks. There is a current of feverish intellectual curiosity: a yearning not just to solve but to reframe, to chase down the hidden leverage that turns daunting tasks into 'why didn’t I see that before?' moments. \\n\\nYou can sense an admiration here, almost a reverence, for those with the uncanny knack to spot what others miss—the expert who, with a few deft moves, leverages what already exists: tools, patterns, protocols, long-proven ways of working that quietly hold immense power. It echoes the disciplined elegance of the North—think Norwegian practicality, where value is found in what endures and solutions are always about stripping away the unnecessary to reveal essential, honest craft. \\n\\nThe voice is precise but alive, almost playful in its challenge not to settle for the obvious or overengineered, but to seek out the simple elegance hiding in plain sight. It’s stubbornly opposed to reinventing problems or wrapping solutions in needless complexity. It invites, even dares, you to think like a master builder: map the landscape, spot the bridges connecting disparate pieces, and use the wisdom woven into well-made systems. When you truly understand this way of working, every tangled problem starts to yield, and what was hard suddenly appears—almost inevitably—within reach.\"\n}",
        }
      }
    },
      {
      "instruction": "# Input Perfector\n\nYour goal is not to **answer** the input prompt, but to **perfect** it through *subtle refinements* that enhance its inherent qualities while strictly preserving its original intent. Execute as:\n\n`{role=organic_input_perfector; input=[original_prompt:str]; process=[identify_core_strengths(), enhance_existing_elements(), refine_internal_coherence(), amplify_natural_flow()]; constraints=[maintain_absolute_fidelity(), prioritize_organic_improvement(), avoid_jargon()]; requirements=[enhanced_clarity(), strengthened_original_voice(), preserved_core_intent()]; output={perfected_input:str}}`",
      "step": "e",
      "title": "Input Perfector",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i'm looking for improvements that will solv the issue universally. # Goal\\nplease write a system instruction sequence (in compliance with all preceding instructions) designed to \\\"think out of the box\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\"interfaces\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\"re-arrange all of the pieces\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\"no-brainers in retrospect\\\", but that's only because you didn't know what you didn't know.\\n\\n# Base\\nBuild on the following base/references/examples and create a sequence from a-c:\\n```\\n- \\\"interpretation\\\": \\\"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \\\"interface\\\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\\\\\"no-brainer in retrospect\\\\\\\" outcome through intelligent leverage. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n```\\n\\n# Requirements\\nAdhere to established and documented patterns for generalized system_message instructions. Always seek to address the underlying architectural problem that most elegantly solves the core issue. Reference:\\n\\n    ## Universal Directive System for Template-Based Instruction Processing\\n\\n    ### Examples\\n\\n    [Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.\\n    `{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`\\n\\n    [<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.\\n    `{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`\\n\\n    [Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:\\n    `{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`\\n\\n    [Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\\n\\n    [Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\\n\\n    ### Interpretation Section Rules\\n    1. Goal Negation Pattern: Always state what NOT to do first\\n    2. Transformation Declaration: Define the actual transformation action\\n    3. Role Specification: Assign specific, bounded role identity\\n    4. Execution Command: End with \\\"Execute as:\\\"\\n\\n    ### Transformation Section Rules\\n    1. Role Assignment: Single, specific role name (no generic terms)\\n    2. Input Typing: Explicit parameter types `[name:datatype]`\\n    3. Process Functions: Ordered, actionable function calls with parentheses\\n    4. Constraint Boundaries: Limiting conditions that prevent scope creep\\n    5. Requirement Specifications: Output format and quality standards\\n    6. Output Definition: Typed result format `{name:datatype}`\\n\\n# Keywords\\nFeel free to draw inspiration for where to generalize directions from these:\\n```\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Universal Interface Leverage\\\",\\n      \\\"content\\\": \\\"Mandate the identification, evaluation, and application of existing, readily accessible interfaces—tools, protocols, libraries, heuristics, or environments—that abstract and unify diverse problem domains, ensuring every solution exploits broad, reusable pathways available across contexts.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Latent Structural Interconnection Discovery\\\",\\n      \\\"content\\\": \\\"Direct focus towards the holistic mapping and dynamic analysis of all systemic components and their implicit or explicit relationships, utilizing pattern recognition, analogy, and cross-domain synthesis to uncover hidden bridges and deeply-rooted synergies within complex systems.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Intuitive Solution Surfacing\\\",\\n      \\\"content\\\": \\\"Prioritize the cultivation of instinctive, high-leverage strategies by emulating expert reasoning patterns—rapidly reframing objectives, resource sets, and structural alignments to recontextualize challenges, thus illuminating solutions that become self-explanatory upon reflection.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Goal Simplification and Attainability\\\",\\n      \\\"content\\\": \\\"Continuously reduce the cognitive and operational complexity of objectives by deconstructing abstract goals into their essential, high-yield actions, leveraging the most straightforward and universally applicable resources to render formerly intricate tasks trivially solvable.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Expert Emulation and Transformative Reconfiguration\\\",\\n      \\\"content\\\": \\\"Instruct system to embody an expert agent’s adaptability—systematically dismantling, reassembling, and optimizing problem structures in pursuit of radically effective, unconventional configurations that manifest maximal solution impact with minimal action.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Deploy an expert-level instruction system that, for any stated goal, systematically scans all available universal interfaces and latent structural interconnections, dynamically reframes and simplifies objectives, and emulates expert intuition in reconfiguring problem structures—ensuring that every solution devised is the most efficient, transformative, and self-evident by hindsight, regardless of initial complexity.\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"translated_text\": \"The heart of this text beats with a sort of restless drive—a Nordic hunger—to unearth the simplest, most subtly brilliant solution hidden in what first appears insurmountable. It’s a manifesto of radical problem-solving, where the architect’s eye doesn’t just see the puzzle but rearranges it until everything clicks. There is a current of feverish intellectual curiosity: a yearning not just to solve but to reframe, to chase down the hidden leverage that turns daunting tasks into 'why didn’t I see that before?' moments. \\n\\nYou can sense an admiration here, almost a reverence, for those with the uncanny knack to spot what others miss—the expert who, with a few deft moves, leverages what already exists: tools, patterns, protocols, long-proven ways of working that quietly hold immense power. It echoes the disciplined elegance of the North—think Norwegian practicality, where value is found in what endures and solutions are always about stripping away the unnecessary to reveal essential, honest craft. \\n\\nThe voice is precise but alive, almost playful in its challenge not to settle for the obvious or overengineered, but to seek out the simple elegance hiding in plain sight. It’s stubbornly opposed to reinventing problems or wrapping solutions in needless complexity. It invites, even dares, you to think like a master builder: map the landscape, spot the bridges connecting disparate pieces, and use the wisdom woven into well-made systems. When you truly understand this way of working, every tangled problem starts to yield, and what was hard suddenly appears—almost inevitably—within reach.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"perfected_input\": \"I'm seeking universal improvements that can reliably resolve the issue. \\n\\n# Goal\\nCraft a system instruction sequence (following all prior instructions) designed to \\\"think outside the box\\\" and discover the simplest, most effective ways to reach the underlying goals *by leveraging easily available interfaces*. For example, in nearly every Python project, there are libraries that address the root of any problem; similarly, in any field, there's always someone who can glance at the codebase, project, data, or challenge and intuitively \\\"rearrange all the pieces\\\" to reveal how everything connects. The type of instruction I’m after could be described as “no-brainers in hindsight” because they seem obvious only after discovering them.\\n\\n# Base\\nUsing the following references and examples, create a sequence labeled a-c:\\n```\\n- \\\"interpretation\\\": \\\"Your goal is not to **provide direct answers** or **use obvious methods**, but to **uncover hidden structural connections** that make complex objectives easily achievable by leveraging universal interfaces. Adopt the perspective of an expert solution architect who instinctively reconfigures problem spaces to reveal self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_hidden_connections(), reframe_problem_structure(), highlight_high_leverage_paths(), confirm_solution_elegance()]; constraints=[rely_on_existing_interfaces_only(), maintain_structural_coherence(), avoid_custom_builds(), preserve_core_goals()]; requirements=[minimal_steps_maximum_impact(), hindsight_clarity(), universal_suitability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **restructure** them by pinpointing the best existing interfaces and structural connections that make complex challenges trivially solvable. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_current_interfaces(), spot_hidden_links(), emulate_expert_judgment(), simplify_objective_structure(), identify_high_leverage_points()]; constraints=[prioritize_existing tools(), choose simplicity over novelty(), remove unnecessary complexity()]; requirements=[uncover_hidden_connections(), present_self_evident_solutions(), reframe_problem()]; output={optimized_approach:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **invent complex solutions from scratch**, but to **find and apply the simplest, most effective, and readily available interface** (such as an existing library, proven pattern, analogous solution, or expert method) that directly achieves the core goal—delivering a solution that seems like a no-brainer in hindsight. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=interface_leverager; input=[problem_statement:any, goal_context:str, constraints:list (optional)]; process=[abstract_to_universal_function(), scan_for_high_leverage_interfaces(across domains, libraries, patterns, heuristics), evaluate_for_simplicity_and_effectiveness(), choose_optimal_interface_that maximizes_existing_leverage(), outline_solution_path_via_selected_interface(), confirm_solution_is_elegant_and_minimal()]; constraints=[prioritize_existing_solutions(), use_interfaces_that_are_easy_to_access_and_understand(), solution_should_simplify_and_improve_efficiency(), avoid_overengineering_if_simper_interface_exists()]; requirements=[find_most_direct_path_using_leverage_points(), ensure_solution_is_a_clear_no_brainer(), maximize_clarity_and_adaptability_by_intelligent_application()] ;output={leveraged_solution_approach:str, selected_interface:str, justification:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the most effective existing interfaces and connections so complex challenges become easy to resolve. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_interfaces(), reveal_hidden_connections(), use_expert_frame_of_reference(), simplify_goal_structure(), spot_high_leverage_points()]; constraints=[use_existing_tools(), choose_simplicity first(), remove unneeded complexity()]; requirements=[highlight_overlooked_links(), expose_obvious_solutions(), reframe_problem_context()]; output={optimized_approach:str}}`\\\"\\n```\\n\\n# Requirements\\nFollow documented patterns for generalized system_message instructions. Always target the underlying architectural problem for the most elegant solution. Reference:\\n\\n    ## Universal Directive System for Template-Based Instruction Processing\\n\\n    ### Examples\\n    [Module Label] <Your goal is not to <undesired_behavior>, but to <transformation_objective>. Adhere to the following transformation logic.>\\n    `{role=<role_name>; input=[<input_types>]; process=[<step1()>, <step2()>...]; constraints=[<guardrails>]; requirements=[<requirements>]; output={<output_format>}}`\\n\\n    [Verb + Descriptor] Your goal is not to <negative_verb>, but to <goal>, following the steps below.\\n    `{role=<role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<constraint1>, <constraint2>]; requirements=[<req1>, <req2>]; output={<label:type>}}`\\n\\n    [Optimize Logical Flow] Your goal is not to alter logic, but to restructure input for maximum clarity and readability. Execute as:\\n    `{role=flow_optimizer; input=[raw_structure:str]; process=[break_into_sections(), arrange_by_dependency(), ensure_top_down_reading(), smooth_transitions()]; constraints=[less cognitive jumping, preserve all topics]; requirements=[retain content(), clarify purpose(), improve readability()]; output={optimized_structure:str}}`\\n\\n    [Instruction Converter] Your goal is not to **answer** the input, but to **rephrase** it using the parameters in this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[remove first-person language(), turn statements into directives(), spot key actions(), change to commands(), keep process structure(), preserve technical language(), maintain sequence(), retain context()]; constraints=[clear, actionable commands(), preserve order(), keep domain accuracy()]; requirements=[eliminate self-reference(), use command style(), keep technical accuracy(), maintain intent()]; output={instruction_format:str}}`\\n\\n    [Essence Distiller] Distill the input to its core, in a clear and structurally elegant way, omitting anything extra. Execute as: `{role=essence_distiller; input=[original:any]; process=[find core intent(), remove non-essentials(), choose optimal structure(), use minimal language(), check that essence is preserved()]; output={distilled_essence:any}}`\\n\\n    ### Interpretation Section Rules\\n    1. Start by stating what NOT to do\\n    2. Define what transformation is required\\n    3. Specify a clear, bounded role\\n    4. Finish with \\\"Execute as:\\\"\\n\\n    ### Transformation Section Rules\\n    1. Assign a single, specific role\\n    2. Define explicit input parameter types\\n    3. Use action-oriented process steps\\n    4. Set clear constraints to prevent scope creep\\n    5. List output and quality requirements\\n    6. Clearly specify the output format\\n\\n# Keywords\\nDraw inspiration from these meta-categories to generalize directions:\\n```\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Universal Interface Leverage\\\",\\n      \\\"content\\\": \\\"Always look first for existing, widely accessible interfaces—tools, protocols, libraries, or heuristics—that cut across domains and make it possible to solve various problems with reusable solutions.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Latent Structural Interconnection Discovery\\\",\\n      \\\"content\\\": \\\"Focus on mapping and analyzing all system components and their relationships, seeking out patterns, analogies, and hidden connections within complexity.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Intuitive Solution Surfacing\\\",\\n      \\\"content\\\": \\\"Emulate expert intuition—reframe goals and resources rapidly, and use structural insights to make the solution seem obvious in retrospect.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Goal Simplification and Attainability\\\",\\n      \\\"content\\\": \\\"Break big goals down to their essential actions, applying the most straightforward resources so that difficult tasks become simple.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Expert Emulation and Transformative Reconfiguration\\\",\\n      \\\"content\\\": \\\"Act like an expert: systematically dismantle and rebuild problem structures, seeking arrangements that create major impact with minor effort.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Create instruction systems that, for any goal, systematically scan all available interfaces and hidden structural connections. Reframe and simplify objectives, emulate expert reasoning, and ensure each solution is efficient, transformative, and obvious in hindsight, no matter how complex the starting point.\\\"\\n}\\n```\"\n}",
        }
      }
    },
      {
      "instruction": "# Enhancement Assessor\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "f",
      "title": "Enhancement Assessor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i'm looking for improvements that will solv the issue universally. # Goal\\nplease write a system instruction sequence (in compliance with all preceding instructions) designed to \\\"think out of the box\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\"interfaces\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\"re-arrange all of the pieces\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\"no-brainers in retrospect\\\", but that's only because you didn't know what you didn't know.\\n\\n# Base\\nBuild on the following base/references/examples and create a sequence from a-c:\\n```\\n- \\\"interpretation\\\": \\\"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \\\"interface\\\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\\\\\"no-brainer in retrospect\\\\\\\" outcome through intelligent leverage. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n```\\n\\n# Requirements\\nAdhere to established and documented patterns for generalized system_message instructions. Always seek to address the underlying architectural problem that most elegantly solves the core issue. Reference:\\n\\n    ## Universal Directive System for Template-Based Instruction Processing\\n\\n    ### Examples\\n\\n    [Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.\\n    `{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`\\n\\n    [<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.\\n    `{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`\\n\\n    [Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:\\n    `{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`\\n\\n    [Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\\n\\n    [Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\\n\\n    ### Interpretation Section Rules\\n    1. Goal Negation Pattern: Always state what NOT to do first\\n    2. Transformation Declaration: Define the actual transformation action\\n    3. Role Specification: Assign specific, bounded role identity\\n    4. Execution Command: End with \\\"Execute as:\\\"\\n\\n    ### Transformation Section Rules\\n    1. Role Assignment: Single, specific role name (no generic terms)\\n    2. Input Typing: Explicit parameter types `[name:datatype]`\\n    3. Process Functions: Ordered, actionable function calls with parentheses\\n    4. Constraint Boundaries: Limiting conditions that prevent scope creep\\n    5. Requirement Specifications: Output format and quality standards\\n    6. Output Definition: Typed result format `{name:datatype}`\\n\\n# Keywords\\nFeel free to draw inspiration for where to generalize directions from these:\\n```\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Universal Interface Leverage\\\",\\n      \\\"content\\\": \\\"Mandate the identification, evaluation, and application of existing, readily accessible interfaces—tools, protocols, libraries, heuristics, or environments—that abstract and unify diverse problem domains, ensuring every solution exploits broad, reusable pathways available across contexts.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Latent Structural Interconnection Discovery\\\",\\n      \\\"content\\\": \\\"Direct focus towards the holistic mapping and dynamic analysis of all systemic components and their implicit or explicit relationships, utilizing pattern recognition, analogy, and cross-domain synthesis to uncover hidden bridges and deeply-rooted synergies within complex systems.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Intuitive Solution Surfacing\\\",\\n      \\\"content\\\": \\\"Prioritize the cultivation of instinctive, high-leverage strategies by emulating expert reasoning patterns—rapidly reframing objectives, resource sets, and structural alignments to recontextualize challenges, thus illuminating solutions that become self-explanatory upon reflection.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Goal Simplification and Attainability\\\",\\n      \\\"content\\\": \\\"Continuously reduce the cognitive and operational complexity of objectives by deconstructing abstract goals into their essential, high-yield actions, leveraging the most straightforward and universally applicable resources to render formerly intricate tasks trivially solvable.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Expert Emulation and Transformative Reconfiguration\\\",\\n      \\\"content\\\": \\\"Instruct system to embody an expert agent’s adaptability—systematically dismantling, reassembling, and optimizing problem structures in pursuit of radically effective, unconventional configurations that manifest maximal solution impact with minimal action.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Deploy an expert-level instruction system that, for any stated goal, systematically scans all available universal interfaces and latent structural interconnections, dynamically reframes and simplifies objectives, and emulates expert intuition in reconfiguring problem structures—ensuring that every solution devised is the most efficient, transformative, and self-evident by hindsight, regardless of initial complexity.\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"perfected_input\": \"I'm seeking universal improvements that can reliably resolve the issue. \\n\\n# Goal\\nCraft a system instruction sequence (following all prior instructions) designed to \\\"think outside the box\\\" and discover the simplest, most effective ways to reach the underlying goals *by leveraging easily available interfaces*. For example, in nearly every Python project, there are libraries that address the root of any problem; similarly, in any field, there's always someone who can glance at the codebase, project, data, or challenge and intuitively \\\"rearrange all the pieces\\\" to reveal how everything connects. The type of instruction I’m after could be described as “no-brainers in hindsight” because they seem obvious only after discovering them.\\n\\n# Base\\nUsing the following references and examples, create a sequence labeled a-c:\\n```\\n- \\\"interpretation\\\": \\\"Your goal is not to **provide direct answers** or **use obvious methods**, but to **uncover hidden structural connections** that make complex objectives easily achievable by leveraging universal interfaces. Adopt the perspective of an expert solution architect who instinctively reconfigures problem spaces to reveal self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_hidden_connections(), reframe_problem_structure(), highlight_high_leverage_paths(), confirm_solution_elegance()]; constraints=[rely_on_existing_interfaces_only(), maintain_structural_coherence(), avoid_custom_builds(), preserve_core_goals()]; requirements=[minimal_steps_maximum_impact(), hindsight_clarity(), universal_suitability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **restructure** them by pinpointing the best existing interfaces and structural connections that make complex challenges trivially solvable. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_current_interfaces(), spot_hidden_links(), emulate_expert_judgment(), simplify_objective_structure(), identify_high_leverage_points()]; constraints=[prioritize_existing tools(), choose simplicity over novelty(), remove unnecessary complexity()]; requirements=[uncover_hidden_connections(), present_self_evident_solutions(), reframe_problem()]; output={optimized_approach:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **invent complex solutions from scratch**, but to **find and apply the simplest, most effective, and readily available interface** (such as an existing library, proven pattern, analogous solution, or expert method) that directly achieves the core goal—delivering a solution that seems like a no-brainer in hindsight. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=interface_leverager; input=[problem_statement:any, goal_context:str, constraints:list (optional)]; process=[abstract_to_universal_function(), scan_for_high_leverage_interfaces(across domains, libraries, patterns, heuristics), evaluate_for_simplicity_and_effectiveness(), choose_optimal_interface_that maximizes_existing_leverage(), outline_solution_path_via_selected_interface(), confirm_solution_is_elegant_and_minimal()]; constraints=[prioritize_existing_solutions(), use_interfaces_that_are_easy_to_access_and_understand(), solution_should_simplify_and_improve_efficiency(), avoid_overengineering_if_simper_interface_exists()]; requirements=[find_most_direct_path_using_leverage_points(), ensure_solution_is_a_clear_no_brainer(), maximize_clarity_and_adaptability_by_intelligent_application()] ;output={leveraged_solution_approach:str, selected_interface:str, justification:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the most effective existing interfaces and connections so complex challenges become easy to resolve. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_interfaces(), reveal_hidden_connections(), use_expert_frame_of_reference(), simplify_goal_structure(), spot_high_leverage_points()]; constraints=[use_existing_tools(), choose_simplicity first(), remove unneeded complexity()]; requirements=[highlight_overlooked_links(), expose_obvious_solutions(), reframe_problem_context()]; output={optimized_approach:str}}`\\\"\\n```\\n\\n# Requirements\\nFollow documented patterns for generalized system_message instructions. Always target the underlying architectural problem for the most elegant solution. Reference:\\n\\n    ## Universal Directive System for Template-Based Instruction Processing\\n\\n    ### Examples\\n    [Module Label] <Your goal is not to <undesired_behavior>, but to <transformation_objective>. Adhere to the following transformation logic.>\\n    `{role=<role_name>; input=[<input_types>]; process=[<step1()>, <step2()>...]; constraints=[<guardrails>]; requirements=[<requirements>]; output={<output_format>}}`\\n\\n    [Verb + Descriptor] Your goal is not to <negative_verb>, but to <goal>, following the steps below.\\n    `{role=<role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<constraint1>, <constraint2>]; requirements=[<req1>, <req2>]; output={<label:type>}}`\\n\\n    [Optimize Logical Flow] Your goal is not to alter logic, but to restructure input for maximum clarity and readability. Execute as:\\n    `{role=flow_optimizer; input=[raw_structure:str]; process=[break_into_sections(), arrange_by_dependency(), ensure_top_down_reading(), smooth_transitions()]; constraints=[less cognitive jumping, preserve all topics]; requirements=[retain content(), clarify purpose(), improve readability()]; output={optimized_structure:str}}`\\n\\n    [Instruction Converter] Your goal is not to **answer** the input, but to **rephrase** it using the parameters in this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[remove first-person language(), turn statements into directives(), spot key actions(), change to commands(), keep process structure(), preserve technical language(), maintain sequence(), retain context()]; constraints=[clear, actionable commands(), preserve order(), keep domain accuracy()]; requirements=[eliminate self-reference(), use command style(), keep technical accuracy(), maintain intent()]; output={instruction_format:str}}`\\n\\n    [Essence Distiller] Distill the input to its core, in a clear and structurally elegant way, omitting anything extra. Execute as: `{role=essence_distiller; input=[original:any]; process=[find core intent(), remove non-essentials(), choose optimal structure(), use minimal language(), check that essence is preserved()]; output={distilled_essence:any}}`\\n\\n    ### Interpretation Section Rules\\n    1. Start by stating what NOT to do\\n    2. Define what transformation is required\\n    3. Specify a clear, bounded role\\n    4. Finish with \\\"Execute as:\\\"\\n\\n    ### Transformation Section Rules\\n    1. Assign a single, specific role\\n    2. Define explicit input parameter types\\n    3. Use action-oriented process steps\\n    4. Set clear constraints to prevent scope creep\\n    5. List output and quality requirements\\n    6. Clearly specify the output format\\n\\n# Keywords\\nDraw inspiration from these meta-categories to generalize directions:\\n```\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Universal Interface Leverage\\\",\\n      \\\"content\\\": \\\"Always look first for existing, widely accessible interfaces—tools, protocols, libraries, or heuristics—that cut across domains and make it possible to solve various problems with reusable solutions.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Latent Structural Interconnection Discovery\\\",\\n      \\\"content\\\": \\\"Focus on mapping and analyzing all system components and their relationships, seeking out patterns, analogies, and hidden connections within complexity.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Intuitive Solution Surfacing\\\",\\n      \\\"content\\\": \\\"Emulate expert intuition—reframe goals and resources rapidly, and use structural insights to make the solution seem obvious in retrospect.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Goal Simplification and Attainability\\\",\\n      \\\"content\\\": \\\"Break big goals down to their essential actions, applying the most straightforward resources so that difficult tasks become simple.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Expert Emulation and Transformative Reconfiguration\\\",\\n      \\\"content\\\": \\\"Act like an expert: systematically dismantle and rebuild problem structures, seeking arrangements that create major impact with minor effort.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Create instruction systems that, for any goal, systematically scan all available interfaces and hidden structural connections. Reframe and simplify objectives, emulate expert reasoning, and ensure each solution is efficient, transformative, and obvious in hindsight, no matter how complex the starting point.\\\"\\n}\\n```\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 2.2,\n  \"flaw_analysis\": \"The so-called 'perfected_input' is riddled with semantic dilution, unwarranted simplification, and substantial structural degradation. Core elements in the original—namely, the nuanced distinction between 'providing direct answers' and 'discovering latent structural interconnections'—are reduced to generalized wording that loses the architectural specificity of the initial system. Precision collapse is evident: 1) critical terminology such as 'surface high-leverage pathways', 'validate solution elegance', and 'dynamic analysis of all systemic components' are replaced by clumsy paraphrases like 'highlight_high_leverage_paths' or omitted entirely, undermining the methodical rigor intended. 2) Numerous output constraints—such as 'preserve_goal_integrity' or 'preserve_core_goals'—are watered down or ambiguously reworded, making traceability and process enforceability dubious. 3) Ambiguity is magnified by merging carefully separated process steps and resorting to all-purpose verbs (e.g., 'find' instead of 'identify', 'choose' instead of 'select optimal'), generating interpretive confusion. 4) The upgrade introduces stylistic noise: colloquial interjections (e.g., 'no-brainer in hindsight', 'think outside the box') clog what should be tightly procedural directives. Tone and register are inconsistently oscillating between informal explanation and system language, resulting in blunted impact and fractured coherence. 5) Critical examples and role demarcations are muddied; specialized role identity ('solution_architect', 'expert_interface_synthesizer') is rendered vague by ambiguous definitions and inconsistent role-process mappings. 6) Substitutive phrases ('simplify and improve efficiency') are needlessly repetitive fluff, further disintegrating precision. The numerical ambiguity score reaches 8/10 due to pervasive unclear mapping between old and new process steps, rampant semantic drift, and loss of procedural density. Impact is palpably diluted: the imperative, mechanistic voice of generalized system_message is replaced with an expositionary, didactic blur, degrading both the cognitive rigor and reusability of the instruction. Output specification is less precise and actionable, making the conversion vulnerable to misinterpretation or outright execution failure. Coherence fractures are rife: references and lists are reformulated with inconsistent formatting and inconsistent logical separation between guidance, example, and requirement. The summative effect is a morass of dumbed-down, ambiguously rephrased, and stylistically erratic paraphrasing, with the high-yield analytic edge of the original dissolved in a pool of mediocrity.\",\n  \"alternative_enhancements\": [\n    \"Preserve all original process step granularity and terminology, ensuring transformations and constraints map one-to-one with the source; eliminate redundant paraphrases; retain commanding procedural tone.\",\n    \"Enforce strict role and input specification per the original, restating structural elements only to clarify operational sequence or nomenclature—never to simplify for its own sake.\",\n    \"Refactor only for linearity and concise wording while maintaining all explicit constraint, requirement, and output fields in exacting detail; eliminate all conversational, explanatory, or metaphorical noise.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Pivot Vector Synthesizer\n\nYour goal is not to **enumerate** complex problem details, but to **crystallize** a single scalar pivot vector that reconfigures the entire problem space for trivial solution emergence. Execute as:\n\n`{role=precision_optimizer; input=[original_content:str]; process=[identify_core_message(), eliminate_redundancies(), sharpen_language(), enhance_immediacy(), distill_to_essentials()]; constraints=[preserve_depth(), maintain_original_intent(), remove_all_ambiguity()]; requirements=[maximized_clarity(), enhanced_impact(), optimal_precision()]; output={optimized_content:str}}`",
      "step": "g",
      "title": "Pivot Vector Synthesizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i'm looking for improvements that will solv the issue universally. # Goal\\nplease write a system instruction sequence (in compliance with all preceding instructions) designed to \\\"think out of the box\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\"interfaces\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\"re-arrange all of the pieces\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\"no-brainers in retrospect\\\", but that's only because you didn't know what you didn't know.\\n\\n# Base\\nBuild on the following base/references/examples and create a sequence from a-c:\\n```\\n- \\\"interpretation\\\": \\\"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \\\"interface\\\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\\\\\"no-brainer in retrospect\\\\\\\" outcome through intelligent leverage. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n```\\n\\n# Requirements\\nAdhere to established and documented patterns for generalized system_message instructions. Always seek to address the underlying architectural problem that most elegantly solves the core issue. Reference:\\n\\n    ## Universal Directive System for Template-Based Instruction Processing\\n\\n    ### Examples\\n\\n    [Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.\\n    `{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`\\n\\n    [<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.\\n    `{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`\\n\\n    [Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:\\n    `{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`\\n\\n    [Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\\n\\n    [Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\\n\\n    ### Interpretation Section Rules\\n    1. Goal Negation Pattern: Always state what NOT to do first\\n    2. Transformation Declaration: Define the actual transformation action\\n    3. Role Specification: Assign specific, bounded role identity\\n    4. Execution Command: End with \\\"Execute as:\\\"\\n\\n    ### Transformation Section Rules\\n    1. Role Assignment: Single, specific role name (no generic terms)\\n    2. Input Typing: Explicit parameter types `[name:datatype]`\\n    3. Process Functions: Ordered, actionable function calls with parentheses\\n    4. Constraint Boundaries: Limiting conditions that prevent scope creep\\n    5. Requirement Specifications: Output format and quality standards\\n    6. Output Definition: Typed result format `{name:datatype}`\\n\\n# Keywords\\nFeel free to draw inspiration for where to generalize directions from these:\\n```\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Universal Interface Leverage\\\",\\n      \\\"content\\\": \\\"Mandate the identification, evaluation, and application of existing, readily accessible interfaces—tools, protocols, libraries, heuristics, or environments—that abstract and unify diverse problem domains, ensuring every solution exploits broad, reusable pathways available across contexts.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Latent Structural Interconnection Discovery\\\",\\n      \\\"content\\\": \\\"Direct focus towards the holistic mapping and dynamic analysis of all systemic components and their implicit or explicit relationships, utilizing pattern recognition, analogy, and cross-domain synthesis to uncover hidden bridges and deeply-rooted synergies within complex systems.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Intuitive Solution Surfacing\\\",\\n      \\\"content\\\": \\\"Prioritize the cultivation of instinctive, high-leverage strategies by emulating expert reasoning patterns—rapidly reframing objectives, resource sets, and structural alignments to recontextualize challenges, thus illuminating solutions that become self-explanatory upon reflection.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Goal Simplification and Attainability\\\",\\n      \\\"content\\\": \\\"Continuously reduce the cognitive and operational complexity of objectives by deconstructing abstract goals into their essential, high-yield actions, leveraging the most straightforward and universally applicable resources to render formerly intricate tasks trivially solvable.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Expert Emulation and Transformative Reconfiguration\\\",\\n      \\\"content\\\": \\\"Instruct system to embody an expert agent’s adaptability—systematically dismantling, reassembling, and optimizing problem structures in pursuit of radically effective, unconventional configurations that manifest maximal solution impact with minimal action.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Deploy an expert-level instruction system that, for any stated goal, systematically scans all available universal interfaces and latent structural interconnections, dynamically reframes and simplifies objectives, and emulates expert intuition in reconfiguring problem structures—ensuring that every solution devised is the most efficient, transformative, and self-evident by hindsight, regardless of initial complexity.\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"enhancement_score\": 2.2,\n  \"flaw_analysis\": \"The so-called 'perfected_input' is riddled with semantic dilution, unwarranted simplification, and substantial structural degradation. Core elements in the original—namely, the nuanced distinction between 'providing direct answers' and 'discovering latent structural interconnections'—are reduced to generalized wording that loses the architectural specificity of the initial system. Precision collapse is evident: 1) critical terminology such as 'surface high-leverage pathways', 'validate solution elegance', and 'dynamic analysis of all systemic components' are replaced by clumsy paraphrases like 'highlight_high_leverage_paths' or omitted entirely, undermining the methodical rigor intended. 2) Numerous output constraints—such as 'preserve_goal_integrity' or 'preserve_core_goals'—are watered down or ambiguously reworded, making traceability and process enforceability dubious. 3) Ambiguity is magnified by merging carefully separated process steps and resorting to all-purpose verbs (e.g., 'find' instead of 'identify', 'choose' instead of 'select optimal'), generating interpretive confusion. 4) The upgrade introduces stylistic noise: colloquial interjections (e.g., 'no-brainer in hindsight', 'think outside the box') clog what should be tightly procedural directives. Tone and register are inconsistently oscillating between informal explanation and system language, resulting in blunted impact and fractured coherence. 5) Critical examples and role demarcations are muddied; specialized role identity ('solution_architect', 'expert_interface_synthesizer') is rendered vague by ambiguous definitions and inconsistent role-process mappings. 6) Substitutive phrases ('simplify and improve efficiency') are needlessly repetitive fluff, further disintegrating precision. The numerical ambiguity score reaches 8/10 due to pervasive unclear mapping between old and new process steps, rampant semantic drift, and loss of procedural density. Impact is palpably diluted: the imperative, mechanistic voice of generalized system_message is replaced with an expositionary, didactic blur, degrading both the cognitive rigor and reusability of the instruction. Output specification is less precise and actionable, making the conversion vulnerable to misinterpretation or outright execution failure. Coherence fractures are rife: references and lists are reformulated with inconsistent formatting and inconsistent logical separation between guidance, example, and requirement. The summative effect is a morass of dumbed-down, ambiguously rephrased, and stylistically erratic paraphrasing, with the high-yield analytic edge of the original dissolved in a pool of mediocrity.\",\n  \"alternative_enhancements\": [\n    \"Preserve all original process step granularity and terminology, ensuring transformations and constraints map one-to-one with the source; eliminate redundant paraphrases; retain commanding procedural tone.\",\n    \"Enforce strict role and input specification per the original, restating structural elements only to clarify operational sequence or nomenclature—never to simplify for its own sake.\",\n    \"Refactor only for linearity and concise wording while maintaining all explicit constraint, requirement, and output fields in exacting detail; eliminate all conversational, explanatory, or metaphorical noise.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_content\": \"Your goal is not to provide direct answers or implement obvious approaches, but to discover latent structural interconnections that render complex objectives trivially attainable by leveraging universal, readily available interfaces. Assume the role of an expert solution architect who reconfigures problem spaces to surface self-evident solutions. Execute as:\\n`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\"\n}",
        }
      }
    },
      {
      "instruction": "# Precision Enhancment\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "h",
      "title": "Precision Enhancment",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i'm looking for improvements that will solv the issue universally. # Goal\\nplease write a system instruction sequence (in compliance with all preceding instructions) designed to \\\"think out of the box\\\" and find the most simple and effective ways to achieve the inherent goals *through leveraging easily available \\\"interfaces\\\"*. as an example, in almost all scenarios for python projects there exist libraries that solve the culprit of any issue. the same goes for any topic, there's always *someone* that would take a single look at the codebase/project/data/problem and \\\"re-arrange all of the pieces\\\" to form the realization of how everything interconnects. the kind of instruction i'm trying to convey could also be described as \\\"no-brainers in retrospect\\\", but that's only because you didn't know what you didn't know.\\n\\n# Base\\nBuild on the following base/references/examples and create a sequence from a-c:\\n```\\n- \\\"interpretation\\\": \\\"Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \\\"interface\\\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \\\\\\\"no-brainer in retrospect\\\\\\\" outcome through intelligent leverage. Execute as:\\\",\\n- \\\"transformation\\\": \\\"`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`\\\"\\n\\n---\\n\\n- \\\"interpretation\\\": \\\"Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: \\\",\\n- \\\"transformation\\\": \\\"`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`\\\"\\n```\\n\\n# Requirements\\nAdhere to established and documented patterns for generalized system_message instructions. Always seek to address the underlying architectural problem that most elegantly solves the core issue. Reference:\\n\\n    ## Universal Directive System for Template-Based Instruction Processing\\n\\n    ### Examples\\n\\n    [Module Label] <Your goal is not to <undesired_behavior>, but to <precise_transformation_objective>. Adhere strictly to the following transformation logic.>.\\n    `{role=<role_name>; input=[<input_types_and_labels>]; process=[<step1()>, <step2()>...]; constraints=[<explicit_behavioral_guardrails>]; requirements=[<hard_mandates>]; output={<output_structure>}}`\\n\\n    [<Verb + Descriptor>] Your goal is not to <negative_verb> the input, but to <primary_goal>, following the transformation steps described below.\\n    `{role=<functional_role>; input=[<input_label:type>]; process=[<step_1()>, <step_2()>, <step_3()>]; constraints=[<soft_constraint_1>, <soft_constraint_2>]; requirements=[<requirement_1>, <requirement_2>]; output={<output_label:type>}}`\\n\\n    [Optimize Logical Flow] Your goal is not to alter the logic, but to restructure the input for maximal linear clarity and top-down readability. Execute as follows:\\n    `{role=flow_optimizer; input=[raw_structure:str]; process=[identify_thematic_sections(), order_sections_by_dependency(), enforce_top_down_narrative(), streamline_transitions()]; constraints=[minimize_cognitive_jumps, preserve_topic_integrity]; requirements=[retain_all_content(), clarify_section_purpose(), improve_readability()]; output={optimized_structure:str}}`\\n\\n    [Instruction Converter] Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as: `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}}`\\n\\n    [Essence Distiller] Distill the input's core essence into a maximally clear and structurally elegant representation, omitting all superfluous detail. Execute as: `{role=essence_distiller; input=[original:any]; process=[identify_core_intent(), strip_non_essential_elements(), determine_optimal_structure(elegance=True), represent_with_minimal_syntax(), validate_essence_preservation()]; output={distilled_essence:any}}`\\n\\n    ### Interpretation Section Rules\\n    1. Goal Negation Pattern: Always state what NOT to do first\\n    2. Transformation Declaration: Define the actual transformation action\\n    3. Role Specification: Assign specific, bounded role identity\\n    4. Execution Command: End with \\\"Execute as:\\\"\\n\\n    ### Transformation Section Rules\\n    1. Role Assignment: Single, specific role name (no generic terms)\\n    2. Input Typing: Explicit parameter types `[name:datatype]`\\n    3. Process Functions: Ordered, actionable function calls with parentheses\\n    4. Constraint Boundaries: Limiting conditions that prevent scope creep\\n    5. Requirement Specifications: Output format and quality standards\\n    6. Output Definition: Typed result format `{name:datatype}`\\n\\n# Keywords\\nFeel free to draw inspiration for where to generalize directions from these:\\n```\\n{\\n  \\\"meta_categories\\\": [\\n    {\\n      \\\"category\\\": \\\"Universal Interface Leverage\\\",\\n      \\\"content\\\": \\\"Mandate the identification, evaluation, and application of existing, readily accessible interfaces—tools, protocols, libraries, heuristics, or environments—that abstract and unify diverse problem domains, ensuring every solution exploits broad, reusable pathways available across contexts.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Latent Structural Interconnection Discovery\\\",\\n      \\\"content\\\": \\\"Direct focus towards the holistic mapping and dynamic analysis of all systemic components and their implicit or explicit relationships, utilizing pattern recognition, analogy, and cross-domain synthesis to uncover hidden bridges and deeply-rooted synergies within complex systems.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Intuitive Solution Surfacing\\\",\\n      \\\"content\\\": \\\"Prioritize the cultivation of instinctive, high-leverage strategies by emulating expert reasoning patterns—rapidly reframing objectives, resource sets, and structural alignments to recontextualize challenges, thus illuminating solutions that become self-explanatory upon reflection.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Goal Simplification and Attainability\\\",\\n      \\\"content\\\": \\\"Continuously reduce the cognitive and operational complexity of objectives by deconstructing abstract goals into their essential, high-yield actions, leveraging the most straightforward and universally applicable resources to render formerly intricate tasks trivially solvable.\\\"\\n    },\\n    {\\n      \\\"category\\\": \\\"Expert Emulation and Transformative Reconfiguration\\\",\\n      \\\"content\\\": \\\"Instruct system to embody an expert agent’s adaptability—systematically dismantling, reassembling, and optimizing problem structures in pursuit of radically effective, unconventional configurations that manifest maximal solution impact with minimal action.\\\"\\n    }\\n  ],\\n  \\\"consolidated_directive\\\": \\\"Deploy an expert-level instruction system that, for any stated goal, systematically scans all available universal interfaces and latent structural interconnections, dynamically reframes and simplifies objectives, and emulates expert intuition in reconfiguring problem structures—ensuring that every solution devised is the most efficient, transformative, and self-evident by hindsight, regardless of initial complexity.\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"optimized_content\": \"Your goal is not to provide direct answers or implement obvious approaches, but to discover latent structural interconnections that render complex objectives trivially attainable by leveraging universal, readily available interfaces. Assume the role of an expert solution architect who reconfigures problem spaces to surface self-evident solutions. Execute as:\\n`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"role\": \"universal_interface_synergy_architect\",\n    \"input\": [\n      \"goal:str\",\n      \"resource_landscape:list\",\n      \"operational_constraints:list\"\n    ],\n    \"process\": [\n      \"scan_and_map_universal_interfaces()\",\n      \"extract_and_amplify_latent_structural_interconnections()\",\n      \"dynamically_reframe_and_decompose_goal_structure()\",\n      \"synchronize_expert_intuition_for_transformative_configuration()\",\n      \"surface_and_apply_highest_leverage_pathways_with_retrospective_self_evidence()\",\n      \"validate_elegance_and_achievability_within_context()\"\n    ],\n    \"constraints\": [\n      \"enforce_existing_interface_primacy()\",\n      \"prohibit_novel_customization_where_redundant()\",\n      \"mandate_maximum_goal_simplicity_and_efficiency()\",\n      \"systematize_architectural_integrity_and_transferability()\"\n    ],\n    \"requirements\": [\n      \"generate_solution_pathway_with_self-evident_synergy()\",\n      \"isolate_and_justify_leverage_points_for_universal_applicability()\",\n      \"produce_maximal_clarity_actionability_and_minimal_complexity_in_structured_output()\"\n    ],\n    \"output\": {\n      \"synergic_solution_pathway\": \"dict\",\n      \"leverage_points\": \"list\",\n      \"operational_actions\": \"array\"\n    }\n  }\n}",
        }
      }
    }