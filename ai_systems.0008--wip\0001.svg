<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 913.5416870117188 1622" style="max-width: 913.5416870117188px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119"><style>#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .error-icon{fill:#a44141;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .edge-thickness-normal{stroke-width:1px;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .marker.cross{stroke:lightgrey;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 p{margin:0;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .cluster-label text{fill:#F9FFFE;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .cluster-label span{color:#F9FFFE;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .cluster-label span p{background-color:transparent;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .label text,#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 span{fill:#ccc;color:#ccc;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .node rect,#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .node circle,#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .node ellipse,#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .node polygon,#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .rough-node .label text,#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .node .label text,#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .image-shape .label,#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .icon-shape .label{text-anchor:middle;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .rough-node .label,#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .node .label,#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .image-shape .label,#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .icon-shape .label{text-align:center;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .node.clickable{cursor:pointer;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .arrowheadPath{fill:lightgrey;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .cluster text{fill:#F9FFFE;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .cluster span{color:#F9FFFE;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 rect.text{fill:none;stroke-width:0;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .icon-shape,#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .icon-shape p,#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .icon-shape rect,#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .coreConfig&gt;*{fill:#e1f5fe!important;stroke:#01579b!important;stroke-width:2px!important;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .coreConfig span{fill:#e1f5fe!important;stroke:#01579b!important;stroke-width:2px!important;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .creation&gt;*{fill:#f3e5f5!important;stroke:#4a148c!important;stroke-width:2px!important;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .creation span{fill:#f3e5f5!important;stroke:#4a148c!important;stroke-width:2px!important;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .storage&gt;*{fill:#e8f5e8!important;stroke:#1b5e20!important;stroke-width:2px!important;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .storage span{fill:#e8f5e8!important;stroke:#1b5e20!important;stroke-width:2px!important;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .catalog&gt;*{fill:#fff3e0!important;stroke:#e65100!important;stroke-width:2px!important;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .catalog span{fill:#fff3e0!important;stroke:#e65100!important;stroke-width:2px!important;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .execution&gt;*{fill:#fce4ec!important;stroke:#880e4f!important;stroke-width:2px!important;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .execution span{fill:#fce4ec!important;stroke:#880e4f!important;stroke-width:2px!important;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .integration&gt;*{fill:#f1f8e9!important;stroke:#33691e!important;stroke-width:2px!important;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .integration span{fill:#f1f8e9!important;stroke:#33691e!important;stroke-width:2px!important;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .interface&gt;*{fill:#e3f2fd!important;stroke:#0d47a1!important;stroke-width:2px!important;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .interface span{fill:#e3f2fd!important;stroke:#0d47a1!important;stroke-width:2px!important;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .issues&gt;*{fill:#ffebee!important;stroke:#c62828!important;stroke-width:3px!important;}#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119 .issues span{fill:#ffebee!important;stroke:#c62828!important;stroke-width:3px!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RulesForAI_Generators_0" d="M423.25,110L423.25,114.167C423.25,118.333,423.25,126.667,423.25,136.333C423.25,146,423.25,157,423.25,162.5L423.25,168"></path><path marker-end="url(#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TemplateConfig_BaseGenerator_1" d="M138,262L138,266.167C138,270.333,138,278.667,145.231,286.686C152.462,294.706,166.924,302.413,174.155,306.266L181.386,310.119"></path><path marker-end="url(#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Generators_BaseGenerator_2" d="M423.25,250L423.25,256.167C423.25,262.333,423.25,274.667,416.019,284.686C408.788,294.706,394.326,302.413,387.095,306.266L379.864,310.119"></path><path marker-end="url(#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BaseGenerator_MDFiles_3" d="M280.625,414L280.625,418.167C280.625,422.333,280.625,430.667,280.625,438.333C280.625,446,280.625,453,280.625,456.5L280.625,460"></path><path marker-end="url(#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MDFiles_StageStructure_4" d="M280.625,542L280.625,548.167C280.625,554.333,280.625,566.667,285.627,578.5C290.629,590.334,300.634,601.667,305.636,607.334L310.638,613.001"></path><path marker-end="url(#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_StageStructure_CatalogManager_5" d="M422.82,694L430.844,698.167C438.868,702.333,454.917,710.667,472.26,718.742C489.603,726.818,508.239,734.635,517.557,738.544L526.875,742.453"></path><path marker-end="url(#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CatalogManager_JSONCatalog_6" d="M623.536,822L623.536,828.167C623.536,834.333,623.536,846.667,623.536,858.333C623.536,870,623.536,881,623.536,886.5L623.536,892"></path><path marker-end="url(#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_JSONCatalog_TemplateCatalog_7" d="M623.536,974L623.536,978.167C623.536,982.333,623.536,990.667,623.536,998.333C623.536,1006,623.536,1013,623.536,1016.5L623.536,1020"></path><path marker-end="url(#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CLI_SequenceExecutor_8" d="M578.63,402L578.63,408.167C578.63,414.333,578.63,426.667,585.453,436.673C592.277,446.679,605.923,454.359,612.746,458.199L619.57,462.038"></path><path marker-end="url(#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UserPrompt_SequenceExecutor_9" d="M826.089,402L826.089,408.167C826.089,414.333,826.089,426.667,817.984,436.712C809.879,446.758,793.669,454.515,785.564,458.394L777.459,462.273"></path><path marker-end="url(#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SequenceExecutor_TemplateCatalog_10" d="M745.931,542L754.402,548.167C762.872,554.333,779.814,566.667,788.284,585.5C796.755,604.333,796.755,629.667,796.755,653C796.755,676.333,796.755,697.667,796.755,719C796.755,740.333,796.755,761.667,796.755,785C796.755,808.333,796.755,833.667,796.755,859C796.755,884.333,796.755,909.667,796.755,933C796.755,956.333,796.755,977.667,786.103,992.269C775.451,1006.871,754.148,1014.742,743.496,1018.678L732.844,1022.614"></path><path marker-end="url(#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SequenceExecutor_SequenceManager_11" d="M735.668,542L742.516,548.167C749.364,554.333,763.059,566.667,769.907,585.5C776.755,604.333,776.755,629.667,776.755,653C776.755,676.333,776.755,697.667,776.755,719C776.755,740.333,776.755,761.667,776.755,785C776.755,808.333,776.755,833.667,776.755,859C776.755,884.333,776.755,909.667,776.755,933C776.755,956.333,776.755,977.667,776.755,999C776.755,1020.333,776.755,1041.667,776.755,1063C776.755,1084.333,776.755,1105.667,772.279,1120.073C767.803,1134.479,758.851,1141.957,754.375,1145.696L749.899,1149.436"></path><path marker-end="url(#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_TemplateCatalog_SequenceManager_12" d="M623.536,1102L623.536,1106.167C623.536,1110.333,623.536,1118.667,628.012,1126.573C632.488,1134.479,641.44,1141.957,645.916,1145.696L650.392,1149.436"></path><path marker-end="url(#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SequenceManager_LiteLLM_13" d="M700.146,1230L700.146,1234.167C700.146,1238.333,700.146,1246.667,700.146,1254.333C700.146,1262,700.146,1269,700.146,1272.5L700.146,1276"></path><path marker-end="url(#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LiteLLM_OutputWriter_14" d="M700.146,1358L700.146,1362.167C700.146,1366.333,700.146,1374.667,700.146,1382.333C700.146,1390,700.146,1397,700.146,1400.5L700.146,1404"></path><path marker-end="url(#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_OutputWriter_JSONOutput_15" d="M700.146,1486L700.146,1490.167C700.146,1494.333,700.146,1502.667,700.146,1510.333C700.146,1518,700.146,1525,700.146,1528.5L700.146,1532"></path><path marker-end="url(#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SequenceExecutor_CatalogManager_16" d="M670.255,542L666.76,548.167C663.265,554.333,656.274,566.667,652.779,585.5C649.284,604.333,649.284,629.667,649.284,653C649.284,676.333,649.284,697.667,647.856,711.882C646.429,726.096,643.574,733.193,642.147,736.741L640.719,740.289"></path><path marker-end="url(#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_StageStructure_TemplateCatalog_17" d="M341.617,694L340.966,698.167C340.315,702.333,339.013,710.667,338.362,725.5C337.711,740.333,337.711,761.667,337.711,785C337.711,808.333,337.711,833.667,337.711,859C337.711,884.333,337.711,909.667,337.711,933C337.711,956.333,337.711,977.667,366.81,994.849C395.909,1012.031,454.107,1025.063,483.206,1031.578L512.305,1038.094"></path><path marker-end="url(#mermaid-1c8c21f7-0969-4251-8cf3-2ad9d6440119_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SequenceExecutor_StageStructure_18" d="M574.754,542L556.158,548.167C537.563,554.333,500.371,566.667,472.963,578.633C445.555,590.6,427.93,602.201,419.118,608.001L410.306,613.801"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(649.2838592529297, 655)" class="edgeLabel"><g transform="translate(-81.72396087646484, -12)" class="label"><foreignObject height="24" width="163.4479217529297"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>INCONSISTENT NAMING</p></span></div></foreignObject></g></g><g transform="translate(337.7109375, 859)" class="edgeLabel"><g transform="translate(-73.296875, -12)" class="label"><foreignObject height="24" width="146.59375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>INCOMPLETE STAGES</p></span></div></foreignObject></g></g><g transform="translate(463.1796875, 579)" class="edgeLabel"><g transform="translate(-47.067710876464844, -12)" class="label"><foreignObject height="24" width="94.13542175292969"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>LEGACY REFS</p></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(423.25, 59)" id="flowchart-RulesForAI-0" class="node default coreConfig"><rect height="102" width="260" y="-51" x="-130" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-100, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>RulesForAI.md<br>📋 Canonical Structure Rules</p></span></div></foreignObject></g></g><g transform="translate(138, 211)" id="flowchart-TemplateConfig-1" class="node default coreConfig"><rect height="102" width="260" y="-51" x="-130" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-100, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>TemplateConfig<br>⚙️ Stage Definitions &amp; Patterns</p></span></div></foreignObject></g></g><g transform="translate(423.25, 211)" id="flowchart-Generators-2" class="node default creation"><rect height="78" width="210.5" y="-39" x="-105.25" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-75.25, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="150.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Template Generators<br>🏭 Python Scripts</p></span></div></foreignObject></g></g><g transform="translate(280.625, 363)" id="flowchart-BaseGenerator-3" class="node default creation"><rect height="102" width="260" y="-51" x="-130" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-100, -36)" style="" class="label"><rect></rect><foreignObject height="72" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>BaseGenerator<br>🔧 Template Creation Engine</p></span></div></foreignObject></g></g><g transform="translate(347.7109375, 655)" id="flowchart-StageStructure-4" class="node default storage"><rect height="78" width="242.6979217529297" y="-39" x="-121.34896087646484" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-91.34896087646484, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="182.6979217529297"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Stage Directory Structure<br>📁 stage1/stage2/stage3</p></span></div></foreignObject></g></g><g transform="translate(280.625, 503)" id="flowchart-MDFiles-5" class="node default storage"><rect height="78" width="209.625" y="-39" x="-104.8125" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-74.8125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="149.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Markdown Templates<br>📄 .md files</p></span></div></foreignObject></g></g><g transform="translate(623.5364646911621, 783)" id="flowchart-CatalogManager-6" class="node default catalog"><rect height="78" width="236.4375" y="-39" x="-118.21875" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-88.21875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="176.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>lvl1_md_to_json.py<br>📊 Catalog Management</p></span></div></foreignObject></g></g><g transform="translate(623.5364646911621, 935)" id="flowchart-JSONCatalog-7" class="node default catalog"><rect height="78" width="227.09375" y="-39" x="-113.546875" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-83.546875, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="167.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>lvl1.md.templates.json<br>🗂️ Template Catalog</p></span></div></foreignObject></g></g><g transform="translate(692.3593788146973, 503)" id="flowchart-SequenceExecutor-8" class="node default execution"><rect height="78" width="253.25" y="-39" x="-126.625" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-96.625, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="193.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>lvl1_sequence_executor.py<br>🚀 Main Execution Engine</p></span></div></foreignObject></g></g><g transform="translate(623.5364646911621, 1063)" id="flowchart-TemplateCatalog-9" class="node default execution"><rect height="78" width="214.65625" y="-39" x="-107.328125" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-77.328125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="154.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>TemplateCatalog<br>🔍 Template Registry</p></span></div></foreignObject></g></g><g transform="translate(700.1458396911621, 1191)" id="flowchart-SequenceManager-10" class="node default execution"><rect height="78" width="233.95834350585938" y="-39" x="-116.97917175292969" style="fill:#fce4ec !important;stroke:#880e4f !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-86.97917175292969, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="173.95834350585938"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>SequenceManager<br>⛓️ Sequence Resolution</p></span></div></foreignObject></g></g><g transform="translate(700.1458396911621, 1319)" id="flowchart-LiteLLM-11" class="node default integration"><rect height="78" width="240.95834350585938" y="-39" x="-120.47917175292969" style="fill:#f1f8e9 !important;stroke:#33691e !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-90.47917175292969, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="180.95834350585938"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>LiteLLM<br>🤖 Multi-Model Interface</p></span></div></foreignObject></g></g><g transform="translate(700.1458396911621, 1447)" id="flowchart-OutputWriter-12" class="node default integration"><rect height="78" width="213.21875" y="-39" x="-106.609375" style="fill:#f1f8e9 !important;stroke:#33691e !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-76.609375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="153.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>StreamingJSONWriter<br>📝 Results Output</p></span></div></foreignObject></g></g><g transform="translate(578.6302108764648, 363)" id="flowchart-CLI-13" class="node default interface"><rect height="78" width="236.0104217529297" y="-39" x="-118.00521087646484" style="fill:#e3f2fd !important;stroke:#0d47a1 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-88.00521087646484, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="176.0104217529297"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Command Line Interface<br>💻 argparse</p></span></div></foreignObject></g></g><g transform="translate(826.0885467529297, 363)" id="flowchart-UserPrompt-14" class="node default interface"><rect height="78" width="158.90625" y="-39" x="-79.453125" style="fill:#e3f2fd !important;stroke:#0d47a1 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-49.453125, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="98.90625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User Prompt<br>👤 Input Text</p></span></div></foreignObject></g></g><g transform="translate(700.1458396911621, 1575)" id="flowchart-JSONOutput-15" class="node default interface"><rect height="78" width="185.1875" y="-39" x="-92.59375" style="fill:#e3f2fd !important;stroke:#0d47a1 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-62.59375, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="125.1875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Execution Results<br>📊 JSON Output</p></span></div></foreignObject></g></g></g></g></g></svg>